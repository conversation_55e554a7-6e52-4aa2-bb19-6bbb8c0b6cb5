import { createSlice } from "@reduxjs/toolkit";

const initialState = {
    targetAssign: {},
  };

const targetAssignedSlice = createSlice({
  name: "TargetAssigned ",
  initialState,
  reducers: {
    setTargetAssign: (state, action) => {
      // console.log(action.payload.data)
      state.targetAssign = action.payload.data;
    },
  },
});
export const { setTargetAssign } = targetAssignedSlice.actions;
export default targetAssignedSlice.reducer;