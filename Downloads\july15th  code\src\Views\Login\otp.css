.otp_form {
  width: 400px;
  margin-left: 15rem;
  padding: 20px;
  margin-top: -6rem;
  border-radius: 15px;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 5px 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  z-index: 1;
  position: relative;
  height: 430px;
}
.submit_otp {
  width: 100%;
  background: #32406d;
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border: 1px solid rgba(0, 0, 0, 0.25);
  background-color: #32406d;
  border-radius: 3px;
  box-sizing: border-box;
  text-align: center;

  height: 40px;
  color: white;
}


@media only screen and (max-width: 542px) {

  .otp_form {
    width: 100%;
    margin-left: 0rem;
    padding: 20px;
    margin-top: 3rem;
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 5px 8px 32px 0 rgba(31, 38, 135, 0.37);
    backdrop-filter: blur(11.5px);
    -webkit-backdrop-filter: blur(11.5px);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.18);
    z-index: 1;
    position: relative;
    height: 430px;
  }

  .otp_form label{

    text-align: left;
  }

  .password-toggle{
   top:48% ;
  }
}