/* Importing Google Fonts - Inter */
@import url('https://fonts.googleapis.com/css2?family=Inter:opsz,wght@14..32,100..900&display=swap');
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Inter", sans-serif;
}
/* body {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(#F4F0FF, #DACDFF);
} */
#chatbot-toggler {
  position: fixed;
  bottom: 10px;
  /* right: 1500px; */
  border: none;
  height: 50px;
  width: 50px;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  z-index: 100;
  border-radius: 50%;
  background: #3506bb;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}
.contaiiner.show-chatbot #chatbot-toggler {
  transform: rotate(90deg);
}
#chatbot-toggler span {
  color: #fff;
  position: absolute;
}
#chatbot-toggler span:last-child,
.contaiiner.show-chatbot #chatbot-toggler span:first-child {
  opacity: 0;
}
.contaiiner.show-chatbot #chatbot-toggler span:last-child {
  opacity: 1;
}
.chatbot-popup {
  position: fixed;
  width: 420px;
  opacity: 0;
  z-index: 9999;
  margin-left: 5%;
 
  /* right: 35px; */
  bottom: 10px;
  pointer-events: none;
  transform: scale(0.2);
  overflow: hidden;
  background: #fff;
  border-radius: 15px;
  transform-origin: bottom left;
  box-shadow: 0 0 128px 0 rgba(0, 0, 0, 0.1),
    0 32px 64px -48px rgba(0, 0, 0, 0.5);
  transition: all 0.1s ease;

}
.contaiiner.show-chatbot .chatbot-popup {
  opacity: 1;
  pointer-events: auto;

  transform: scale(1);
}
.chatbot-popup .chat-header {
  display: flex;
  padding: 15px 22px;
  align-items: center;
  background: #3506bb;
  justify-content: space-between;
}
.chat-header .header-info {
  display: flex;
  gap: 10px;
  align-items: center;
}
.header-info svg {
  width: 35px;
  height: 35px;
  flex-shrink: 0;
  padding: 6px;
  fill: #3506bb;
  background: #fff;
  border-radius: 50%;
}
.header-info .logo-text {
  color: #fff;
  font-weight: 600;
  font-size: 1.31rem;
  letter-spacing: 0.02rem;
}
.chat-header button {
  border: none;
  height: 40px;
  width: 40px;
  color: #fff;
  cursor: pointer;
  padding-top: 2px;
  margin-right: -10px;
  font-size: 1.9rem;
  border-radius: 50%;
  background: none;
  transition: 0.2s ease;
}
.chat-header button:hover {
  background: #593bab;
}
.chat-body {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 460px;
  overflow-y: auto;
  margin-bottom: 82px;
  padding: 25px 22px;
  scrollbar-width: thin;
  scrollbar-color: #DDD3F9 transparent;
}
.chat-body .message {
  display: flex;
  gap: 11px;
  align-items: center;
}
.chat-body .message svg {
  width: 35px;
  height: 35px;
  flex-shrink: 0;
  padding: 6px;
  fill: #fff;
  align-self: flex-end;
  margin-bottom: 2px;
  background: #3506bb;
  border-radius: 50%;
}
.chat-body .message .message-text {
  padding: 12px 16px;
  max-width: 75%;
  font-size: 0.95rem;
  word-wrap: break-word;
  white-space: pre-line;
}
.chat-body .message.error .message-text {
  color: #ff0000;
}
.chat-body .bot-message .message-text {
  background: #F6F2FF;
  border-radius: 13px 13px 13px 3px;
}
.chat-body .user-message {
  flex-direction: column;
  align-items: flex-end;
}
.chat-body .user-message .message-text {
  color: #fff;
  background: #3506bb;
  border-radius: 13px 13px 3px 13px;
}
.chat-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  background: #fff;
  padding: 15px 22px 20px;
}
.chat-footer .chat-form {
  display: flex;
  align-items: center;
  position: relative;
  background: #fff;
  border-radius: 32px;
  outline: 1px solid #CCCCE5;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.06);
}
.chat-form:focus-within {
  outline: 2px solid #3506bb;
}
.chat-form .message-input {
  width: 100%;
  height: 47px;
  border: none;
  outline: none;
  font-size: 0.95rem;
  padding: 0 17px;
  background: none;
}
.chat-form #send-message {
  height: 35px;
  width: 35px;
  border: none;
  flex-shrink: 0;
  color: #fff;
  cursor: pointer;
  display: none;
  margin-right: 6px;
  background: #3506bb;
  border-radius: 50%;
  font-size: 1.15rem;
  transition: 0.2s ease;
}
.chat-form .message-input:valid~#send-message {
  display: block;
}
.chat-form #send-message:hover {
  background: #593bab;
}
/* Responsive media query for mobile screens */
@media (max-width: 520px) {
  #chatbot-toggler {
    right: 20px;
    bottom: 20px;
  }
  .chatbot-popup {
    right: 0;
    bottom: 0;
    height: 100%;
    border-radius: 0;
    width: 100%;
  }
  .chatbot-popup .chat-header {
    padding: 12px 15px;
  }
  .chat-body {
    height: calc(90% - 55px);
    padding: 25px 15px;
  }
  .chat-footer {
    padding: 10px 15px 15px;
  }
}