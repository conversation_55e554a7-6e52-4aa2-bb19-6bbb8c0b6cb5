import { px } from 'framer-motion';
import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Cartes<PERSON>G<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';

// Helper function to convert skills to numeric indices
const getSkillsIndex = (skills) => {
    const uniqueSkills = [...new Set(skills)];
    return uniqueSkills.reduce((acc, skill, index) => {
        acc[skill] = index;
        return acc;
    }, {});
};

// Function to format labels into multiple lines
const wrapText = (text, maxWordsPerLine = 1) => {
    const words = text.split(' ');
    let wrappedText = '';
    for (let i = 0; i < words.length; i += maxWordsPerLine) {
        wrappedText += words.slice(i, i + maxWordsPerLine).join(' ') + '\n';
    }
    return wrappedText.trim();
};

const CustomTick = ({ x, y, payload, skills }) => {
    const skillName = skills[payload.value];
    const maxLineLength = 7; // Set a max character limit for each line
    const lines = [];

    for (let i = 0; i < skillName.length; i += maxLineLength) {
        lines.push(skillName.substring(i, i + maxLineLength)); // Break skillName into chunks
    }

    return (
        <g transform={`translate(${x + 5},${y})`}>
            {lines.map((line, index) => (
                <text
                    key={index}
                    x={0}
                    y={index * 15} // Adjust spacing between lines
                    textAnchor="middle"
                    fontSize="13px"
                    fill="#32406d"
                >
                    {line}
                </text>
            ))}
        </g>
    );
};


const ScatterPlot = ({ skillsData, onDotClick, width = 1000, height=400}) => {
    const uniqueSkills = skillsData.map(item => item['Skill/Domain']);
    const skillsIndex = uniqueSkills.reduce((acc, skill, index) => {
        acc[skill] = index;
        return acc;
    }, {});

    const mappedData = skillsData.map(item => ({
        x: skillsIndex[item['Skill/Domain']],
        y: item['Relevance Score'],
        Experience: item.Experience,
        SkillDomain: item['Skill/Domain']
    }));


    return (
        <div>
            <div style={{ position: 'relative', marginTop: "10px", height:"100%", overflow: "scroll" }}>
            <ScatterChart
                width={width}
                height={height}
                className='scatterplot '
                margin={{ top: 20, right: 20, bottom:40, left: 20 }}
            
            >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                    type="category"
                    dataKey="x"
                    name="Skill"
                    interval={0}
                    tick={<CustomTick skills={uniqueSkills} />}
                    tickMargin={15}
                    label={{
                        value: 'Skills',
                        angle: 0,
                        position: 'insideBottom', // Positions the label near the x-axis line
                        offset:-55,               // Adjust the offset to move closer or farther
                        style: { fill: 'rgb(136, 132, 216)', fontSize: '20px',fontWeight:"500" }, // Style for the label
                    }}
                />
                <YAxis
                    type="number"
                    dataKey="y"
                    name="Relevance Scores"
                    label={{ value: 'Relevence Scores',  angle: -90, position: 'insideLeft',style:{fill: 'rgb(136, 132, 216)',fontSize:"20px",fontWeight:"500",},  dy: 60 }}
                    
                    ticks={[0, 1, 2, 3, 4, 5]}
                    domain={[0, 5]}
                />
                <Tooltip />
                <Legend />
                <Scatter
                
                    data={mappedData}
                    fill="rgb(136, 132, 216)"
                    onClick={onDotClick}
                />
            </ScatterChart>
            </div>
        </div>
    );
};

export default ScatterPlot;