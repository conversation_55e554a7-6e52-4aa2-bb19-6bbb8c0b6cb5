import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  datesSelected:[],
  jobIdsSelected:[],
  clientsSelected:[],
  profilesSelected:[],
  recruitersSelected:[],
  statussSelected:[]
};

const jobfilterSlice = createSlice({
  name: "Jobs data",
  initialState,
  reducers: {
    setDatesSelected: (state, action) => {
      state.datesSelected = action.payload.data;
    },
   
    setjobIdsSelected: (state, action) => {
      state.jobIdsSelected = action.payload.data;
    },
    
    setclientsSelected: (state, action) => {
      state.clientsSelected = action.payload.data;
    },
    setprofilesSelected: (state, action) => {
      state.profilesSelected = action.payload.data;
    },
 
    setrecruitersSelected: (state, action) => {
      state.recruitersSelected = action.payload.data;
    },
    setstatussSelected: (state, action) => {
      state.statussSelected = action.payload.data;
    },
  },
});

export const {setDatesSelected,setjobIdsSelected,setclientsSelected,setprofilesSelected,setrecruitersSelected,setstatussSelected} = jobfilterSlice.actions;
export default jobfilterSlice.reducer;
