.background {
  position: relative;
  width: 100%;
  height: 100vh;
  /* background-image: url("src/assets/Mako.jpg"); */
  background-size: cover;
  background-position: center;
}
.top-nav {
  position: absolute;
  top: 0vh;
  display: flex;
  text-align: left;
  margin-left: -20vw;
}

.logo-div {
  position: absolute;
  top: 50px;
  left: 0px;
}

.top-right {
  position: absolute;
  top: 45vh;
  margin-left: 17vw;
}

.login-links {
  margin-left: 8vw;
  position: absolute;
  top: 60vh;
  display: flex;
  text-align: center;
}

.R {
  appearance: none;
  background-color: #fff;
  border: 0.125em solid #fff;
  border-radius: 0.9375em;
  box-sizing: border-box;
  color: #414143;
  cursor: pointer;
  display: inline-block;
  font-family:
    Roobert,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Helvetica,
    Arial,
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol";
  font-size: 17px;
  font-weight: 600;
  line-height: normal;
  margin: 0;
  min-height: 3.55em;
  min-width: 0;
  outline: none;
  padding: 1em 2em;
  text-align: center;
  text-decoration: none;
  transition: all 300ms cubic-bezier(0.23, 1, 0.32, 1);
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
  will-change: transform;
  margin-right: 10px;
  /* margin-left: 35px; */
}

.R:disabled {
  pointer-events: none;
}

.R:hover {
  color: blue;
  background-color: #fff;
  box-shadow: rgba(0, 0, 0, 0.25) 0 8px 15px;
  transform: translateY(-2px);
  border: 0.125em solid white;
}

.R:active {
  box-shadow: none;
  transform: translateY(0);
  border: 0.125em solid black;
}

.logo {
  background-color: rgba(255, 255, 255, 0.422);
  height: 70px;
  width: 110px;
  margin-top: 35px;
  margin-left: 83vw;
  border: 0.125em solid rgba(255, 255, 255, 0.326);
  border-radius: 5px;
  padding: 5px;
}

@media only screen and (max-width: 1600px) and (min-width: 900px) {
  .logo {
    background-color: rgba(255, 255, 255, 0.422);
    height: 70px;
    width: 110px;
    margin-top: 30px;
    margin-left: 83vw;
    border: 0.125em solid rgba(255, 255, 255, 0.326);
    border-radius: 5px;
    padding: 5px;
  }
}
@media only screen and (max-width: 542px) {
  .login-links {
    position: absolute;
    display:block;
    text-align: center;
    margin-left: 30vw
  }
  .top-right{
    align-items: center;
    margin-left: 30vw;
    margin-top: 6vh;

  }
 
    
  .logedmob{
    top:-20%;
    display: block;
    text-align: center;
    position:absolute;
  }
  .login-links  .R{
    margin-bottom: 15px;
  }

  .R {
    appearance: none;
    background-color: #fff;
    border: 0.125em solid white;
    border-radius: 0.9375em;
    box-sizing: border-box;
    color: #414143;
    cursor: pointer;
    display:block;
   align-items: center;
    font-size: 17px;
    font-weight: 600;
    line-height: normal;
    margin: 0;
    min-height: 3.55em;
    min-width: 0;
    outline: none;
    padding: 1em 2em;
    text-align: center;
    text-decoration: none;
    transition: all 300ms cubic-bezier(0.23, 1, 0.32, 1);
    user-select: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    will-change: transform;
    margin-right: 10px;
    /* margin-left: 35px; */
  }
}