import React, { useState, useEffect } from "react";
import "./UnifiedAuthPage.css";
import Logo from "../../assets/Logo.png";
import BackgroundImg from "../../assets/MLs.jpg";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { RiEyeLine, RiEyeOffLine } from "react-icons/ri";
import Modal from "react-modal";
import Cookies from "universal-cookie";
import { useParams, useLocation } from "react-router-dom";
import { ThreeDots } from "react-loader-spinner";
import CryptoJS from "crypto-js";

const cookies = new Cookies();

function UnifiedAuthPage() {
  const location = useLocation();
  const navigate = useNavigate();
  const isSessionLogout = location.state?.isPopupvisible;

  // State for toggling between login and registration
  const [isLoginMode, setIsLoginMode] = useState(true);
  
  // State for popups and loading
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const [waitForSubmission, setWaitForSubmission] = useState(false);
  const [modalIsOpen, setModalIsOpen] = useState(false);
  const [modalMessage, setModalMessage] = useState("");
  
  // State for password visibility
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Login credentials state
  const [loginCredentials, setLoginCredentials] = useState({
    username: "",
    password: "",
  });
  
  // Registration credentials state
  const [registerCredentials, setRegisterCredentials] = useState({
    username: "",
    password: "",
    confirmPassword: "",
    name: "",
    email: "",
  });
  
  // Error states
  const [loginError, setLoginError] = useState({
    username_error: "",
    password_error: "",
  });
  
  const [registerError, setRegisterError] = useState({
    username_error: "",
    password_error: "",
    confirmPassword_error: "",
    name_error: "",
    email_error: "",
  });

  useEffect(() => {
    if (localStorage.getItem("profileImage")) {
      localStorage.removeItem('profileImage');
    }
  }, []);

  useEffect(() => {
    if (isSessionLogout) {
      setIsPopupVisible(true);
    }
  }, [isSessionLogout]);

  // Toggle between login and registration modes
  const toggleAuthMode = () => {
    setIsLoginMode(!isLoginMode);
    // Clear errors when switching modes
    setLoginError({ username_error: "", password_error: "" });
    setRegisterError({
      username_error: "",
      password_error: "",
      confirmPassword_error: "",
      name_error: "",
      email_error: "",
    });
  };

  // Handle login form changes
  const handleLoginChange = (e) => {
    const { name, value } = e.target;
    if (!waitForSubmission) {
      setWaitForSubmission(true);
      setLoginCredentials({ ...loginCredentials, [name]: value });

      // Clear the error message when the field is changed
      if (name === "username") {
        setWaitForSubmission(false);
        setLoginError((prevError) => ({
          ...prevError,
          username_error: "",
        }));
      } else if (name === "password") {
        setWaitForSubmission(false);
        setLoginError((prevError) => ({
          ...prevError,
          password_error: "",
        }));
      }
    }
  };

  // Handle registration form changes
  const handleRegisterChange = (e) => {
    const { name, value } = e.target;
    if (!waitForSubmission) {
      setWaitForSubmission(true);
      setRegisterCredentials({ ...registerCredentials, [name]: value });

      // Clear the error message when the field is changed
      setWaitForSubmission(false);
      setRegisterError((prevError) => ({
        ...prevError,
        [`${name}_error`]: "",
      }));
    }
  };

  // Password visibility toggles
  const togglePasswordVisibility = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword((prevShowPassword) => !prevShowPassword);
  };

  // Handle forgot password
  const handleForgotPassword = (e) => {
    e.preventDefault();
    navigate("/ForgotPassword");
  };

  // Login validation and submission
  const handleLoginSubmit = async (e) => {
    if (!waitForSubmission) {
      setWaitForSubmission(true);
      e.preventDefault();

      // Validate username
      if (loginCredentials.username.length === 0) {
        setWaitForSubmission(false);
        setLoginError((loginError) => ({
          ...loginError,
          username_error: "Username field cannot be empty",
        }));
        return;
      } else {
        setLoginError((loginError) => ({ ...loginError, username_error: "" }));
      }

      // Validate password
      if (loginCredentials.password.length === 0) {
        setWaitForSubmission(false);
        setLoginError((loginError) => ({
          ...loginError,
          password_error: "Password field cannot be empty",
        }));
        return;
      } else {
        setLoginError((loginError) => ({ ...loginError, password_error: "" }));
      }

      const secretKey = "ATS@mako";
      const encryptedPassword = CryptoJS.AES.encrypt(
        loginCredentials.password,
        secretKey
      ).toString();

      try {
        const response = await fetch('https://backend.makonissoft.com/login/recruiter', {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            username: loginCredentials.username,
            password: encryptedPassword,
            user_type: "recruiter",
            user_id: localStorage.getItem("user_id"),
          }),
        });

        if (response.ok) {
          const data = await response.json();

          cookies.set("USERNAME", loginCredentials.username, {
            path: "/",
          });
          cookies.set("USERTYPE", "recruiter", {
            path: "/",
          });

          if (data.status === "error") {
            setWaitForSubmission(false);
            toast.error(data.message);
          } else {
            const userName = data.name || 'Default Name';
            const userEmail = data.email || 'Default Email';
            
            localStorage.setItem('user_id', data.user_id);
            localStorage.setItem('username', data.username);
            localStorage.setItem('name', userName);
            localStorage.setItem('email', userEmail);

            navigate(data.redirect, {
              state: {
                user_type: "recruitment",
                user_id: data.user_id,
                user_name: loginCredentials.username,
                name: userName,
                email: userEmail
              },
            });
          }
        } else {
          setWaitForSubmission(false);
          toast.error("An error occurred while processing your request.");
        }
      } catch (err) {
        setWaitForSubmission(false);
        toast.error("An error occurred while processing your request.");
      }
    }
  };

  // Registration validation
  const validateRegistration = () => {
    let isValid = true;
    const errors = {
      username_error: "",
      password_error: "",
      confirmPassword_error: "",
      name_error: "",
      email_error: "",
    };

    // Username validation
    if (registerCredentials.username.length === 0) {
      errors.username_error = "Username field cannot be empty";
      isValid = false;
    }

    // Name validation
    if (registerCredentials.name.length === 0) {
      errors.name_error = "Name field cannot be empty";
      isValid = false;
    }

    // Email validation
    if (registerCredentials.email.length === 0) {
      errors.email_error = "Email field cannot be empty";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(registerCredentials.email)) {
      errors.email_error = "Please enter a valid email address";
      isValid = false;
    }

    // Password validation
    if (registerCredentials.password.length === 0) {
      errors.password_error = "Password field cannot be empty";
      isValid = false;
    } else if (registerCredentials.password.length < 6) {
      errors.password_error = "Password must be at least 6 characters long";
      isValid = false;
    }

    // Confirm password validation
    if (registerCredentials.confirmPassword.length === 0) {
      errors.confirmPassword_error = "Please confirm your password";
      isValid = false;
    } else if (registerCredentials.password !== registerCredentials.confirmPassword) {
      errors.confirmPassword_error = "Passwords do not match";
      isValid = false;
    }

    setRegisterError(errors);
    return isValid;
  };

  // Registration submission handler
  const handleRegisterSubmit = async (e) => {
    e.preventDefault();

    if (!waitForSubmission) {
      setWaitForSubmission(true);

      if (!validateRegistration()) {
        setWaitForSubmission(false);
        return;
      }

      try {
        const response = await fetch("https://backend.makonissoft.com/signup", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            username: registerCredentials.username,
            name: registerCredentials.name,
            email: registerCredentials.email,
            password: registerCredentials.password,
            user_type: "recruiter",
          }),
        });

        const responseData = await response.json();

        if (responseData.status === "error") {
          toast.error(responseData.message);
          setWaitForSubmission(false);
        } else {
          setModalMessage("Account created successfully! Verification link has been sent to your email.");
          setModalIsOpen(true);
          setWaitForSubmission(false);

          // Clear form
          setRegisterCredentials({
            username: "",
            password: "",
            confirmPassword: "",
            name: "",
            email: "",
          });

          // Switch to login mode after successful registration
          setTimeout(() => {
            setIsLoginMode(true);
            setModalIsOpen(false);
          }, 3000);
        }
      } catch (error) {
        setWaitForSubmission(false);
        toast.error("An error occurred while creating your account.");
      }
    }
  };

  return (
    <div className="unified-auth-container">
      <img className="background-image" src={BackgroundImg} alt="Background" />
      <div className="logo-container">
        <img className="logo" src={Logo} alt="logo" />
      </div>
      
      <div className="auth-form-container">
        <div className="auth-toggle">
          <button 
            className={`toggle-btn ${isLoginMode ? 'active' : ''}`}
            onClick={() => setIsLoginMode(true)}
          >
            Login
          </button>
          <button 
            className={`toggle-btn ${!isLoginMode ? 'active' : ''}`}
            onClick={() => setIsLoginMode(false)}
          >
            Register
          </button>
        </div>

        {isLoginMode ? (
          // Login Form
          <form onSubmit={handleLoginSubmit} className="auth-form">
            <h1 className="form-title">HR Portal Login</h1>
            <p className="form-subtitle">Sign in to your account</p>
            
            <div className="form-group">
              <label htmlFor="username">Username:</label>
              <input 
                id="username" 
                name="username" 
                value={loginCredentials.username}
                onChange={handleLoginChange}
                className="form-input"
              />
              {loginError.username_error && (
                <div className="error-message">
                  {loginError.username_error}
                </div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="password">Password:</label>
              <div className="password-input-container">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={loginCredentials.password}
                  onChange={handleLoginChange}
                  className="form-input"
                />
                <span
                  className="password-toggle"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? <RiEyeLine /> : <RiEyeOffLine />}
                </span>
              </div>
              {loginError.password_error && (
                <div className="error-message">
                  {loginError.password_error}
                </div>
              )}
            </div>

            <button type="submit" className="submit-btn" disabled={waitForSubmission}>
              {waitForSubmission ? (
                <ThreeDots
                  visible={true}
                  height="20"
                  width="20"
                  color="white"
                  ariaLabel="loading"
                />
              ) : (
                "Sign In"
              )}
            </button>

            <a
              className="forgot-password-link"
              onClick={handleForgotPassword}
            >
              Forgot Password?
            </a>
          </form>
        ) : (
          // Registration Form
          <form onSubmit={handleRegisterSubmit} className="auth-form">
            <h1 className="form-title">Create Account</h1>
            <p className="form-subtitle">Join our HR portal</p>

            <div className="form-group">
              <label htmlFor="reg-name">Full Name:</label>
              <input
                id="reg-name"
                name="name"
                value={registerCredentials.name}
                onChange={handleRegisterChange}
                className="form-input"
              />
              {registerError.name_error && (
                <div className="error-message">
                  {registerError.name_error}
                </div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="reg-email">Email:</label>
              <input
                id="reg-email"
                name="email"
                type="email"
                value={registerCredentials.email}
                onChange={handleRegisterChange}
                className="form-input"
              />
              {registerError.email_error && (
                <div className="error-message">
                  {registerError.email_error}
                </div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="reg-username">Username:</label>
              <input
                id="reg-username"
                name="username"
                value={registerCredentials.username}
                onChange={handleRegisterChange}
                className="form-input"
              />
              {registerError.username_error && (
                <div className="error-message">
                  {registerError.username_error}
                </div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="reg-password">Password:</label>
              <div className="password-input-container">
                <input
                  id="reg-password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={registerCredentials.password}
                  onChange={handleRegisterChange}
                  className="form-input"
                />
                <span
                  className="password-toggle"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? <RiEyeLine /> : <RiEyeOffLine />}
                </span>
              </div>
              {registerError.password_error && (
                <div className="error-message">
                  {registerError.password_error}
                </div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="reg-confirm-password">Confirm Password:</label>
              <div className="password-input-container">
                <input
                  id="reg-confirm-password"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={registerCredentials.confirmPassword}
                  onChange={handleRegisterChange}
                  className="form-input"
                />
                <span
                  className="password-toggle"
                  onClick={toggleConfirmPasswordVisibility}
                >
                  {showConfirmPassword ? <RiEyeLine /> : <RiEyeOffLine />}
                </span>
              </div>
              {registerError.confirmPassword_error && (
                <div className="error-message">
                  {registerError.confirmPassword_error}
                </div>
              )}
            </div>

            <button type="submit" className="submit-btn" disabled={waitForSubmission}>
              {waitForSubmission ? (
                <ThreeDots
                  visible={true}
                  height="20"
                  width="20"
                  color="white"
                  ariaLabel="loading"
                />
              ) : (
                "Create Account"
              )}
            </button>
          </form>
        )}
      </div>

      {/* Session Timeout Modal */}
      <Modal
        isOpen={isPopupVisible}
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            backdropFilter: "blur(0.5px)",
            zIndex: 9999,
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
          content: {
            width: "350px",
            height: "160px",
            margin: "auto",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            background: "white",
            borderRadius: "10px",
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
            padding: "20px 20px 10px",
          },
        }}
      >
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>Session Timeout</h2>
            </div>
            <div className="modal-body">
              <p>Your session has timed out. You have been logged out</p>
            </div>
            <div className="modal-footer">
              <button
                className="modal-ok"
                onClick={() => {
                  setIsPopupVisible(false);
                  navigate("/UnifiedAuth");
                }}
              >
                Ok
              </button>
            </div>
          </div>
        </div>
      </Modal>

      {/* Registration Success Modal */}
      <Modal
        isOpen={modalIsOpen}
        onRequestClose={() => setModalIsOpen(false)}
        contentLabel="Registration Success"
        className="success-modal"
        overlayClassName="modal-overlay"
      >
        <div className="modal-content">
          <p>{modalMessage}</p>
          <button onClick={() => setModalIsOpen(false)}>OK</button>
        </div>
      </Modal>
    </div>
  );
}

export default UnifiedAuthPage;
