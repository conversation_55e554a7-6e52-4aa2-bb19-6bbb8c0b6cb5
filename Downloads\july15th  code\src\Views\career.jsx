import React, { useEffect, useState } from 'react';
import './career.css'; // Create this CSS file for styles

const Careers = () => {
  const [jobPosts, setJobPosts] = useState([]);

  useEffect(() => {
    fetch('http://192.168.1.26:5002/api/job_posts')
      .then(res => res.json())
      .then(data => setJobPosts(Array.isArray(data) ? data : []))
      .catch(() => console.error('Error fetching job data'));
  }, []);

  const openApplyModal = (index) => {
    const modal = document.querySelector(`.modal_multi[data-job-index='${index}']`);
    modal?.classList.add('active');
  };

  const closeModal = (e) => {
    e.target.closest('.modal')?.classList.remove('active');
  };

  const openJdModal = (jobid) => {
    document.getElementById(`jd-modal-${jobid}`)?.classList.add('active');
  };

  const closeJdModal = (e) => {
    e.target.closest('.jd-modal')?.classList.remove('active');
  };

  const downloadJD = (base64, filename, extension) => {
    const element = document.createElement("a");
    element.setAttribute("href", `data:application/${extension};base64,${base64}`);
    element.setAttribute("download", `${filename}.${extension}`);
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const formatJD = (jd_raw = '') => {
    const lines = jd_raw.replace(/\\r/g, '').split(/\n+/);
    let jd_html = '', in_list = false;
    lines.forEach(line => {
      line = line.trim();
      if (!line) return;

      if (/:$/.test(line) || /^[A-Z\s]+$/.test(line)) {
        if (in_list) { jd_html += '</ul>'; in_list = false; }
        jd_html += `<h3>${line}</h3>`;
      } else if (/^[•\-*\u2022]+\s*/.test(line)) {
        if (!in_list) { jd_html += '<ul>'; in_list = true; }
        jd_html += `<li>${line.replace(/^[•\-*\u2022]+\s*/, '')}</li>`;
      } else {
        if (in_list) { jd_html += '</ul>'; in_list = false; }
        jd_html += `<p>${line}</p>`;
      }
    });
    if (in_list) jd_html += '</ul>';
    return jd_html;
  };

  return (
    <section className="py-5 pt-70">
      <div className="container section-padding">
        <h2 className="text-center">Current Openings</h2>
        <div className="jobcontainer table-container">
          <table className="job-listing-table">
            <thead>
              <tr>
                <th>Role</th>
                <th>Mode</th>
                <th>Contract Type</th>
                <th>Location</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {jobPosts.map((job, index) => {
                const jd_html = formatJD(job.jd_pdf_text);
                const has_jd = job.jd_pdf_text?.trim();
                return (
                  <React.Fragment key={job.id}>
                    <tr>
                      <td><a href="#" onClick={(e) => { e.preventDefault(); openApplyModal(index); }}>{job.role}</a></td>
                      <td>{job.mode}</td>
                      <td>Full time</td>
                      <td>{job.location}</td>
                      <td>
                        <div style={{ display: 'flex' }}>
                          {has_jd && (
                            <button
                              className="btntype view-jd-btn"
                              onClick={() => openJdModal(job.id)}>
                              View JD
                            </button>
                          )}
                          <button
                            className="btntype"
                            style={{ fontSize: 15, fontWeight: 500, backgroundColor: '#32406d', color: '#fff' }}
                            onClick={() => openApplyModal(index)}>
                            Apply
                          </button>
                        </div>
                      </td>
                    </tr>

                    {/* JD Modal */}
                    {has_jd && (
                      <div id={`jd-modal-${job.id}`} className="jd-modal">
                        <div className="jd-modal-content">
                          <div className="jd-modal-header">
                            <h2>{job.role} - Job Description</h2>
                            <div className="jd-modal-actions">
                              {job.jd_pdf_base64 && (
                                <a href="javascript:void(0)" onClick={() => downloadJD(job.jd_pdf_base64, `JD_${job.role}`, job.jd_pdf_extension)} className="jd-download-btn">
                                  <i className="fa-solid fa-file-arrow-down"></i>
                                </a>
                              )}
                              <span className="jd-close" onClick={closeJdModal}><i className="fa-solid fa-xmark"></i></span>
                            </div>
                          </div>
                          <div className="jd-modal-body">
                            <div className="full-jd-content" dangerouslySetInnerHTML={{ __html: jd_html }} />
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Apply Modal */}
                    <div className="modal modal_multi" data-job-index={index}>
                      <div className="modal-content">
                        <div className="modal-header">
                          <h2 className="modal-title">{job.role}</h2>
                          <span className="close close_multi" onClick={closeModal}><i className="fa-solid fa-xmark"></i></span>
                        </div>
                        <div className="modal-body">
                          <div className="job-details-grid">
                            <div className="job-detail-item"><span className="detail-label">Experience :</span><span className="detail-value">{job.experience_min} - {job.experience_max} years</span></div>
                            <div className="job-detail-item"><span className="detail-label">Location :</span><span className="detail-value">{job.location}</span></div>
                            <div className="job-detail-item"><span className="detail-label">Skills :</span><span className="detail-value">{job.skills}</span></div>
                            <div className="job-detail-item"><span className="detail-label">Work Mode :</span><span className="detail-value">{job.mode}</span></div>
                            <div className="job-detail-item"><span className="detail-label">Shift Timings :</span><span className="detail-value">{job.shift_timings}</span></div>
                            <div className="job-detail-item"><span className="detail-label">Position :</span><span className="detail-value">{job.no_of_positions}</span></div>
                          </div>
                          <div className="modal-footer">
                            <a href="#modal" className="btn btn-primary wide lightbox-link apply-btn" style={{ color: '#fff' }} data-jobid={job.id} data-jobtitle={job.role} data-jobclient={job.client}>
                              Apply Now
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </React.Fragment>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </section>
  );
};

export default Careers;