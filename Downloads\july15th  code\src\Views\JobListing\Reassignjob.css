@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600&display=swap");

/* body {
				font-family: 'Poppins', sans-serif;
				background: #cad1ff;
			} */

h1 {
  text-align: center;
}

.container_RA {
  position: relative;
  margin: auto;
  width: 80%;
  /* Adjusted width for responsiveness */
  max-width: 500px;
  /* Added maximum width for smaller screens */
  margin-top: 10px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  overflow: auto;
}

.container_RA form table tbody tr th {
  color: black;
  font-size: 18px;
  width: 170px;
  text-align: right;
}
.container_RA form table tbody tr {
  margin-bottom: 15px;
}
.container_RA form table tbody tr td {
  text-align: left;
  padding-left: 10px;
}
.container_RA table {
  padding-bottom: 10px;
}
.btn_RA {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  max-width: 200px;
  width: 100%;
  padding: 5px;
  border: none;
  outline: none;
  color: #fff;
  border-radius: 5px;
  margin: 25px 0;
  background-color: #32406d;
  transition: all 0.3s linear;
  cursor: pointer;
  margin: auto;
  text-decoration: none;
  /* Added text-decoration for the link style */
}

@media screen and (max-width: 767px) {
  .container_RA {
    width: 90%;
    /* Adjusted width for smaller screens */
    padding: 5px;
    /* Reduced padding for smaller screens */
  }

  h1 {
    font-size: 18px;
  }

  label {
    margin-right: 0;
    /* Removed the margin for better alignment */
    text-align: center;
    display: block;
    /* Added block display for center alignment */
    margin-bottom: 5px;
    /* Added margin at the bottom for spacing */
  }

  select {
    width: 100%;
    
  }

  .btn_RA {
    width: 100%;
    /* Full width button for smaller screens */
  }
}

/* Additional styles for the dropdown */
/* select {
				width: 100%;
				padding: 8px;
			
				border: 1px solid #ccc;
				border-radius: 5px;
				font-size: 14px;
				margin: auto;
			}

			select option[disabled] {
				color: #999;
				font-size: 15px;
				font-weight: 200;
				text-align: center;
			} */
