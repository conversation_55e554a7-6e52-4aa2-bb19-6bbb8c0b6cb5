/* UnifiedAuthPage.css */

.unified-auth-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.logo-container {
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.logo {
  height: 80px;
  width: auto;
}

.auth-form-container {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 450px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 5;
  position: relative;
  margin-top: 60px;
}

.auth-toggle {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toggle-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  font-size: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn.active {
  background: rgba(50, 64, 109, 0.9);
  color: white;
  box-shadow: 0 4px 15px rgba(50, 64, 109, 0.4);
}

.toggle-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.auth-form {
  width: 100%;
}

.form-title {
  text-align: center;
  color: white;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.form-subtitle {
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin-bottom: 30px;
  font-weight: 400;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: white;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.form-input {
  width: 100%;
  padding: 14px 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  font-size: 16px;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: rgba(50, 64, 109, 0.8);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(50, 64, 109, 0.2);
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-toggle {
  position: absolute;
  right: 16px;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.7);
  font-size: 20px;
  z-index: 10;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: white;
}

.submit-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #32406d 0%, #4a5a8a 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
  box-shadow: 0 4px 15px rgba(50, 64, 109, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 52px;
}

.submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2a3659 0%, #3d4a7a 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(50, 64, 109, 0.5);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.forgot-password-link {
  display: block;
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 14px;
  margin-top: 20px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.forgot-password-link:hover {
  color: white;
  text-decoration: underline;
}

.error-message {
  color: #ff6b6b;
  font-size: 13px;
  margin-top: 5px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 15px;
  padding: 30px;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header h2 {
  color: #32406d;
  margin-bottom: 15px;
  font-size: 22px;
}

.modal-body p {
  color: #666;
  margin-bottom: 25px;
  line-height: 1.5;
}

.modal-ok {
  background: #32406d;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.3s ease;
}

.modal-ok:hover {
  background: #2a3659;
}

.success-modal {
  background: white;
  border-radius: 15px;
  padding: 30px;
  max-width: 400px;
  margin: auto;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-content {
  color: #32406d;
}

.modal-content p {
  margin-bottom: 20px;
  line-height: 1.5;
}

.modal-content button {
  background: #32406d;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.3s ease;
}

.modal-content button:hover {
  background: #2a3659;
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-form-container {
    margin: 20px;
    padding: 30px 25px;
    max-width: none;
    width: calc(100% - 40px);
  }
  
  .logo {
    height: 60px;
  }
  
  .form-title {
    font-size: 24px;
  }
  
  .form-subtitle {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .auth-form-container {
    margin: 15px;
    padding: 25px 20px;
    width: calc(100% - 30px);
  }
  
  .logo-container {
    top: 20px;
  }
  
  .logo {
    height: 50px;
  }
  
  .form-title {
    font-size: 22px;
  }
  
  .toggle-btn {
    padding: 10px 15px;
    font-size: 14px;
  }
  
  .form-input {
    padding: 12px 14px;
    font-size: 14px;
  }
  
  .submit-btn {
    padding: 14px;
    font-size: 15px;
  }
}
