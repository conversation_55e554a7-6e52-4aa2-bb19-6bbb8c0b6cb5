@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600&display=swap");

.users {
  display: flex;
  padding-top: 40px;
  font-weight: 700;
  font-size: 18px;
  text-align: center;
  justify-content: center;
  color: #000000;
}
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 26px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.sliderUserAccount {
  position: absolute;
  cursor: pointer;
  top: 8px;
  left: -1px;
  right: 6px;
  bottom: -2px;
  background-color: #e84f4f;
  transition: 0.4s;
}

.sliderUserAccount:before {
  position: absolute;
  content: "";
  height: 17px;
  width: 17px;
  border-radius: 50%;
  background-color: white;
  transition: 0.4s;
  top: 1.5px;
  left: 1.5px;
}

input:checked + .sliderUserAccount {
  background-color: #51ac51;
}

input:checked + .sliderUserAccount:before {
  transform: translateX(25px);
}

.sliderUserAccount.round {
  border-radius: 26px;
}

.sliderUserAccount.round:before {
  border-radius: 50%;
}
.userac tbody tr {
  border-bottom: 2px solid yellow;
}
.container .userac thead {
  height: 5px;
}

.table {
  width: 100%;
  border-collapse: collapse; /* Ensures borders are displayed as expected */
}

.userac tbody tr {
  border-bottom: 1px solid rgba(105, 105, 105, 0.856); /* Applies a border to each row */
}
.userac thead tr th {
  font-size: 14px;
  padding: 10px 2px;
}
.userac tbody tr td {
  border-right: none;
  font-size: 14px; /* Applies a border to each row */
}

.table tbody tr:nth-child(odd) {
  background-color: #f9f9f9; /* Applies alternating row colors */
}
.td_UA td {
  border: 1px solid #ddd;
  height: 15px;
  text-align: left;
}

.loader-container {
  position: absolute;
  top: calc(50% - 50px); /* 50% of the viewport height minus 50px */
  left: calc(50% - 50px); /* 50% of the viewport width minus 50px */
}
.UAsearch{
  margin-top: 4px;
  padding-left: 26px;
  height: 30px;
  width: 200px;
  background-color: rgba(255, 255, 255, 0.80);
  border: none;
  border-radius:5px;
  padding: 0 25px
}
@media screen and (max-height: 680px) and (min-width: 1000px) {
  /* .container {
    height: 68vh;
    margin-bottom: 40px;
  } */
  .section {
    margin-left: 190px !important;
  }
/* 
  .pagnBar {
    margin-top: -38px;
  } */
}
@media screen and (max-width: 767px) {
 .AUheading{
  margin-top: 25px;
 }
}
@media screen and (min-width:320px) and (max-width:375px) {
  .UAsearch{
    width: 200px ;
}
}
@media screen and (min-width:375px) and (max-width:425px){
  .UAsearch{
    width: 250px ;
}
}

/*  single reditrect of those buttons */

.useraccco {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  /* padding: 10px; */
}

.left-section {
  display: flex;
  gap: 10px;
}

.center-section {
  flex: 1;
 margin-left: 105px;
  text-align: center;
}

.right-section {
  display: flex;
  align-items: center;
}

.search-container {
  display: flex;
  align-items: center;
  position: relative;
}

.UAsearch {
  padding: 5px;
  padding-left: 30px;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.search-icon {
  position: absolute;
  left: 8px;
  font-size: 18px;
  color: #32406d;
}

.remove_filter_icons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px;
  border-radius: 5px;
  cursor: pointer;
}

.useracccot{
  
    background-color: rgb(50, 64, 109);
    color: white;
    padding: 5px;
    border: none;
    width: auto;
    border-radius: 5px;

}

