@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600&display=swap");

/* NOTIFICATION BADGE */
.badge {
  position: absolute;
  top: -10px;
  right: -2px;
  padding: 5px 10px;
  border-radius: 50%;
  background: red;
  color: white;
}

/* UPDATE CANDIDATE */
.title {
  text-align: center;
  color: #333;
  /* text-align: center; */
  margin-bottom: 10px;
}

.Form_UC h2 {
  color: #000000;
  /* margin-top: 20px; */
}

.Form_UC {
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);

  border-radius: 5px;
  padding: 20px;
  /* width: 25rem; */
  width: 90%;
  margin: auto;
  display: flex;
  /* align-items: center;
    justify-content: center; */
  flex-direction: column;
}

p {
  color: rgb(0, 0, 0);
  margin-bottom: 10px;
}

.UCS {
  padding: 5px;
  width: 400px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 3px;
  box-sizing: border-box;
  margin-bottom: 10px;
}

input[type="submit"] {
  max-width: 160px;
  padding: 8px;
  background-color: #32406d;
  color: #fff;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
}

/* input[type='submit']:hover {
    background-color: #555;
} */

.update-container {
  padding-top: 0rem;
}

.textarea {
  resize: none;
  padding-left: 5px;
  /* max-width: 100%; */
  /* box-sizing: border-box; */
  width: 400px;
  height: 100px;
}

/* For desktop screens */
@media screen and (min-width: 1024px) {
  .tbuc th,
  .tbuc td {
    padding: 0px 30px;
    font-size: 16px;
  }
}

/* For tablet screens */
@media screen and (max-width: 1023px) and (min-width: 768px) {
  /* .container {
        overflow: auto;
    } */
  .Form_UC {
    overflow-x: scroll;
  }

  .tbuc th,
  .tbuc td {
    padding: 5px 20px;
    font-size: 14px;
  }
}

/* For mobile screens */
@media screen and (max-width: 767px) {
  .Form_UC {
    max-width: 22rem;
    overflow-x: scroll;
  }

  .wrapper .section .top_navbar .heading h1 {
    font-size: 18px;
    margin-top: 5px;
    /* margin-right: 150px; */
  }

  .update-container {
    padding-top: 4rem;
  }

  .logo {
    margin-left: 10px;
  }

  select {
    width: 180px;
  }
  .Textarea {
    width: 180px;
  }
}

/* sidebar Dropdown */
.dropdown-sidebar:hover .dropdown-menu-sidebar {
  display: block;
}

.dropdown-menu-sidebar {
  display: none;
  color: #333333;
  text-align: right;
  list-style: none;
}

.tbuc {
  /* width: 100%; */
  border-collapse: collapse;
  border: 1px solid #180202;
}
.th {
  border-collapse: collapse;
  border: 1px solid #180202;
}
.tr {
  border-collapse: collapse;
  border: 1px solid #180202;
}
.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #32406d;
  border: none;
  border-radius: 50%; /* Make the button round */
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  font-size: 24px; /* Icon size */
  color: white; /* Icon color */
}

/*multi level dropdown select*/

.Form_UC h2 {
  color: #000000;
  /* margin-top: 20px; */
}

.Form_UC {
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);

  border-radius: 5px;
  padding: 20px;
  /* width: 25rem; */
  width: 90%;
  margin: auto;
  display: flex;
  /* align-items: center;
      justify-content: center; */
  flex-direction: column;
}

p {
  color: rgb(0, 0, 0);
  margin-bottom: 10px;
}

.UCS {
  padding: 5px;
  width: 400px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 3px;
  box-sizing: border-box;
  margin-bottom: 10px;
}

input[type="submit"] {
  max-width: 160px;
  padding: 8px;
  background-color: #32406d;
  color: #fff;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
}

/* input[type='submit']:hover {
      background-color: #555;
  } */

.update-container {
  padding-top: 0rem;
}

.textarea {
  resize: none;
  padding-left: 5px;
  /* max-width: 100%; */
  /* box-sizing: border-box; */
  width: 400px;
  height: 100px;
}

/* For desktop screens */
@media screen and (min-width: 1024px) {
  .tbuc th,
  .tbuc td {
    padding: 0px 30px;
    font-size: 16px;
  }
}

/* For tablet screens */
@media screen and (max-width: 1023px) and (min-width: 768px) {
  /* .container {
          overflow: auto;
      } */
  .Form_UC {
    overflow-x: scroll;
  }

  .tbuc th,
  .tbuc td {
    padding: 5px 20px;
    font-size: 14px;
  }
}

/* For mobile screens */
@media screen and (max-width: 767px) {
  .Form_UC {
    max-width: 22rem;
    overflow-x: scroll;
  }

  .wrapper .section .top_navbar .heading h1 {
    font-size: 18px;
    margin-top: 5px;
    /* margin-right: 150px; */
  }

  .update-container {
    padding-top: 4rem;
  }

  .logo {
    margin-left: 10px;
  }

  select {
    width: 180px;
  }
  .Textarea {
    width: 180px;
  }
}

/* sidebar Dropdown */
.dropdown-sidebar:hover .dropdown-menu-sidebar {
  display: block;
}

.dropdown-menu-sidebar {
  display: none;
  color: #333333;
  text-align: right;
  list-style: none;
}

.tbuc {
  /* width: 100%; */
  border-collapse: collapse;
  border: 1px solid #180202;
}
.th {
  border-collapse: collapse;
  border: 1px solid #180202;
}
.tr {
  border-collapse: collapse;
  border: 1px solid #180202;
}
.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #32406d;
  border: none;
  border-radius: 50%; /* Make the button round */
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  font-size: 24px; /* Icon size */
  color: white; /* Icon color */
}

/*multi level dropdown select*/
.menu {
  --menu-height: 0px;
  box-sizing: border-box;
  position: relative;
  top: 0;
  left: 0;
  display: inline-flex;
  box-shadow: 0 12px 35px 0 rgba(255, 235, 167, 0.15);
}
.menu ul {
  list-style: none;
  padding: 13px 0px;
  margin: 0;
}
.menu ul li,
.menu ul li a {
  opacity: 0.8;
  color: #ffffff;
  cursor: pointer;
  transition: 200ms;
  text-decoration: none;
  white-space: nowrap;
  font-weight: 700;
}
.menu ul li:hover,
.menu ul li a:hover {
  opacity: 1;
}
.menu ul li a,
.menu ul li a a {
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
}
.menu ul li {
  padding-right: 36px;
}
.menu ul li::before {
  content: "";
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #000000;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}
.menu ul .link::before {
  padding-right: 0;
  display: none;
}
.menu > ul {
  display: flex;
  height: var(--menu-height);
  align-items: center;
  background-color: #ffffff40;
}
.menu > ul li {
  position: relative;
  margin: 0 8px;
  width: 300px;
}
.menu > ul li ul {
  visibility: hidden;
  opacity: 0;
  width: 20px;
  min-width: 210px;
  background-color: #ffffff;
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  transition: 200ms;
  transition-delay: 200ms;
  padding: 5px;
  z-index: 2;
  border-radius: 4px;
}
.menu > ul li ul .rightarrow {
  right: -50px;
}
.menu > ul li ul li {
  margin: 0;
  padding: 5px;
  font-family: "Roboto", sans-serif;
  font-weight: 500;
  font-size: 15px;
  border-radius: 5px;
  border-radius: 2px;
  padding: 5px 0;
  color: #000000;
  padding-left: 20px;
  padding-right: 15px;
  text-align: left;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-right: 40px;
  width: 200px;
}
.menu > ul li ul li::before {
  content: "";
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 5px solid #000000;
}
.menu > ul li ul li ul {
  top: -2%;
  left: 100%;
  transform: translate(0);
}
.menu > ul li ul li:hover {
  background-color: #102770;
  color: #fff;
}
.menu > ul li:hover > ul {
  opacity: 1;
  visibility: visible;
  transition-delay: 0ms;
}
.for-dropdown {
  /*border: 1px solid #a5a2a2;*/

  position: relative;
  font-weight: 400;
  font-size: 15px;
  line-height: 2;
  color: #000;
  min-width: 200px;
  transition: all 200ms linear;
  border-radius: 4px;
  letter-spacing: 1px;
  padding: 10px;
  display: inline-flex;
  align-items: center;
}
.updatecandidatetable {
  display: flex;
  margin-top: 50px;
  padding-left: 20px;
}
.title {
  font-size: 20px;
  padding-left: 35%;
}
@media screen and (max-width: 542px) {
  .updatecandidatetable {
    display: flex;
    margin-top: 50px;
    padding-left: 2px;
  }
  .title {
    padding-left: 10%;
  }
  .tbuc thead tr th {
    /* width: 100%; */
    padding: 5px;
  }
  .tbuc tbody tr td {
    padding: 5px;
  }
  .update-container {
    padding-top: 1rem;
  }
  .menu > ul li {
    width: 100%;
  }

  body.active .updatecandidatetable,
  body.active .update-container {
    display: flex !important;
  }
  body.barside2 .updatecandidatetable,
  body.barside2 .update-container {
    display: none;
  }
  body.barside2 .candiedit {
    display: none;
  }
}

/* --------------------------------------Target Recruiter Selection multi select css----------------------- */

.multiSelectContainer {
  position: relative;
  text-align: left;
  width: 100%;
}

.recruiter-selection2 .searchWrapper {
  border: 1px solid #aaa !important;
  border-radius: 4px;
  background-color: #fff;
  min-height: 10px !important;
  padding: 3px !important ;
  position: absolute !important;
  margin-top: 0px;
  width: 100%;
  display: flex;
  overflow-x: auto; /* Enable horizontal scrolling */
  white-space: nowrap;
}

.multiSelectContainer li {
  padding: 5px !important;
}
.chip {
  align-items: center;
  background: #0096fb;
  border-radius: 11px;
  color: #fff;
  display: inline-flex;
  font-size: 13px;
  line-height: 19px;
  margin-bottom: 5px;
  margin-right: 5px;
  padding: 4px 5px !important;
  white-space: nowrap;
}

.recruiter-selection2 {
  /* height:100px; */
  background-color: red;
}

.recruiter-selection2 .multiselect-container {
  position: relative;
}

.recruiter-selection2 .multiselect-container .multiselect-dropdown {
  width: 200px; /* Adjust width as needed */
  padding-right: 30px; /* Make space for the down arrow */
}

.recruiter-selection {
  position: relative;
}

.recruiter-selection2 .optionListContainer {
  position: absolute !important;
  bottom: 5px;
  background-color: #fff;
  width: 100% !important;
  top: 50px !important;
  /* background-color: red !important; */
}

.recruiter-selection2.chip {
  align-items: center;
  background: #0096fb;
  border-radius: 11px;
  color: #fff;
  display: inline-flex;
  font-size: 13px;
  line-height: 19px;
  margin: 0px !important;
  padding: 4px 4px !important;
}
/* .recruiter-selection .multiselect-container::after {
  content: "▼ ";
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  font-size: 12px;
  color: #888;
} */

.multiSelectContainer ul {
  max-height: 136px;
  background-color: aliceblue;
}

.multiSelectContainer ul::-webkit-scrollbar {
  width: 4px;
}

.highlightOption {
  background: none !important;
  color: #000000 !important;
}
.highlightOption:hover {
  background: #0096fb !important;
  color: #fff !important;
}

/* This for Assign Requirment  forms  css  */
.signup-content2 {
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(11.5px);
  border-radius: 10px;
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
  -o-border-radius: 10px;
  margin-top: auto;
  -ms-border-radius: 10px;
  padding: 16px 40px;
}
.containeracco2 {
  width: 480px;
  position: relative;
  margin: 0 auto;
  margin-top: 7%;
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  height: auto;
  position: relative;
}

@media screen and (max-width: 480px) {
  .signup-content2 {
    padding: 50px 25px;
  }
}

@media screen and (max-width: 767px) {
  .containeracco2 {
    width: 380px !important;
    height: 500px !important;
  }
  .signup-content2 {
    height: 500px !important;
  }

  .input-icon {
    width: 390px;
  }

  #user_type {
    width: 85% !important;
  }

  .acclabel {
    text-align: left;
  }
}

@media screen and (min-width: 320px) and (max-width: 375px) {
  .containeracco2 {
    width: 280px !important;
    height: 400px !important;
  }

  .signup-content2 {
    width: 280px !important;
    height: 400px !important;
  }
  .input-icon {
    width: 280px !important;
  }
  .form-input {
    width: 240px !important;
  }
  #submit {
    width: 104% !important;
  }
  .signup-form {
    margin-top: -25px !important;
  }
}

@media screen and (min-width: 375px) and (max-width: 425px) {
  .containeracco2 {
    width: 330px !important;
    height: 400px !important;
  }

  .signup-content2 {
    width: 330px !important;
    height: 400px !important;
  }
  .input-icon {
    width: 330px !important;
  }
  .form-input {
    width: 280px !important;
  }
  #submit {
    width: 104% !important;
  }
  .signup-form {
    margin-top: -25px !important;
  }
}
@media only screen and (max-width: 542px) {
  body.active .containeracco2 {
    display: block !important;
  }

  body.barside2 .containeracco2 {
    display: none;
  }

  body.active .wrapper .sidebar {
    left: -300px !important;
  }

  .wrapper .section {
    width: calc(100% - 300px);
    margin-left: 300px;
    transition: all 0.5s ease;
    display: flex;
    flex-direction: column;
    height: 100vh;
  }
}

select optgroup[label="--Select Recruiter--"] {
  color: orange;
  font-weight: bold;
}

select optgroup[label="--Select Manager--"] {
  color: orange;
  font-weight: bold;
}
