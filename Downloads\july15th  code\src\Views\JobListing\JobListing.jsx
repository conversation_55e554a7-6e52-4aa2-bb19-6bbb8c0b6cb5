import LeftNav from "../../Components/LeftNav";
import "../../Components/leftnav.css";
import TitleBar from "../../Components/TitleBar";
import "../../Components/titlenav.css";
import { MdFilterAlt } from "react-icons/md";
import "./joblisting.css";
import { useCallback, useEffect, useState, useMemo } from "react";
import { toast } from "react-toastify";
import { Bounce } from "react-toastify";
import { useNavigate } from "react-router-dom";
import { FaUserPlus, FaTrash, FaSort, FaSortUp, FaSortDown } from "react-icons/fa";
import { BiSort } from "react-icons/bi";
import { BiSortUp, BiSortDown } from 'react-icons/bi';
import { useRef } from "react";
import { TbStatusChange } from "react-icons/tb";
import { FaTrashAlt } from "react-icons/fa";
import { Tooltip as ReactTooltip } from "react-tooltip";
import { ThreeDots } from "react-loader-spinner";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import filter_icon from '../../assets/filter_icon.svg'
import clear_search from '../../assets/clear_search.svg'
import { getDashboardData, getAllJobs } from "../utilities";
import { MdOutlineYoutubeSearchedFor } from "react-icons/md";
import { PiMicrosoftExcelLogoFill } from "react-icons/pi";
import {
  setDatesSelected,
  setjobIdsSelected,
  setclientsSelected,
  setprofilesSelected,
  setrecruitersSelected, setstatussSelected
} from '../../store/slices/jobfilterSlice'
import * as XLSX from "xlsx";
import { IoMdSearch } from "react-icons/io";
import { RxUpdate } from "react-icons/rx";
import { VscClearAll } from "react-icons/vsc";

import {
  faRedo,
  faClock,
  faEdit,
  faBook,
} from "@fortawesome/free-solid-svg-icons";
import { FaAngleLeft } from "react-icons/fa6";
import { FaAngleRight } from "react-icons/fa6";
import Modal from "react-modal";
import { Hourglass } from "react-loader-spinner";
// import { useSelector } from "react-redux";
import { store } from "../../store/store";
import { useDispatch, useSelector } from "react-redux";
import { setAllJobs } from "../../store/slices/jobSlice";
import { setDashboardData } from "../../store/slices/dashboardSlice";
function JobListing() {
  const dispatch = useDispatch();
  const [belowCount, setBelowCount] = useState(null);

  // const { 
  //   datesSelected: datesSelectedRdx,
  //   jobIdsSelected: jobIdsSelectedRdx,

  //   clientsSelected: clientsSelectedRdx,
  //   profilesSelected: profilesSelectedRdx,

  //   recruitersSelected: recruitersSelectedRdx,
  //   statussSelected: statussSelectedRdx

  // } = useSelector((state) => state.jobfilterSliceReducer);

  const { jobs } = useSelector((state) => state.jobSliceReducer);

  const [searchValue, setSearchValue] = useState("");
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const [showItems, setShowItems] = useState([]);
  const [allJobs, setJobs] = useState();
  const [id, setId] = useState(1);
  const [countItems, setCountItems] = useState(0);
  const [filteredId, setFilteredId] = useState([]);

  const [showModal1, setShowModal1] = useState(false);
  const [deleteItemId, setDeleteItemId] = useState(null); // Track the item id to delete

  const handleShowModal1 = (id) => {
    setDeleteItemId(id); // Set the item id to be deleted
    setShowModal1(true);
  };

  const handleCloseModal1 = () => {
    setShowModal1(false);
  };

  const handleConfirmDelete = () => {
    if (deleteItemId) {
      handleDeleteJob(deleteItemId);
      setDeleteItemId(null); // Reset delete item id
    }
  };

  const uniRef = useRef(null);

  const [waitForSubmission, setwaitForSubmission] = useState(false);
  const { dashboardData } = useSelector((state) => state.dashboardSliceReducer);
  useEffect(() => {
    const closeFilterPop = (e) => {
      const allRefIds = [
        "job_postRef",
        "job_idRef",
        "job_statusRef",
        "client_ref",
        "recruiter_ref",
        "role_ref",

        "job_label_postRef",
        "job_label_idRef",
        "job_label_statusRef",
        "client_label_ref",
        "recruiter_label_ref",
        "role_label_ref",

      ];
      let bool = false;
      for (const ref of allRefIds) {
        if (document.getElementById(ref)?.contains(e.target)) {
          bool = true;
          return;
        }
      }
      if (uniRef?.current?.contains(e.target) || bool) {
        console.log("yes");
      } else {
        console.log("no");
        setshowSearchjoblisting((prev) => ({
          ...Object.fromEntries(Object.keys(prev).map((key) => [key, false])),
        }));
      }
    };
    document.addEventListener("click", closeFilterPop);
    return () => {
      document.removeEventListener("click", closeFilterPop);
    };
  }, []);
  // useEffect(() => {
  //   if (Object.keys(dashboardData).length === 0) {

  //     getDashboardData();
  //   } else {
  //   }
  // }, [dashboardData]);
  // console.log(dashboardData);

  const [selectAllDate, setSelectAllDate] = useState(false);
  const [uniqueDataDate, setuniqueDataDate] = useState([]);
  const [dateSelected, setdateSelected] = useState([]);

  const [selectAllForJobId, setselectAllForJobId] = useState(false);
  const [uniqueDatajobId, setuniqueDatajobId] = useState([]);
  const [jobIdSelected, setjobIdSelected] = useState([]);

  const [selectAllStatus, setSelectAllStatus] = useState(false);
  const [uniqueDataStatus, setuniqueDataStatus] = useState([]);
  const [statusSelected, setstatusSelected] = useState([]);

  const [selectAllClient, setSelectAllClient] = useState(false);
  const [uniqueDataClient, setuniqueDataClient] = useState([]);
  const [clientSelected, setclientSelected] = useState([]);

  const [selectAllRecruiter, setSelectAllRecruiter] = useState(false);
  const [uniqueDataRecruiter, setuniqueDataRecruiter] = useState([]);
  const [recruiterSelected, setrecruiterSelected] = useState([]);

  const [selectAllProfile, setSelectAllProfile] = useState(false);
  const [uniqueDataProfile, setuniqueDataProfile] = useState([]);
  const [profileSelected, setprofileSelected] = useState([]);
  //filtering


  // useEffect(() => {
  //   store.dispatch(setDatesSelected({ data: dateSelected }))

  //   store.dispatch(setjobIdsSelected({ data: jobIdSelected }))

  //   store.dispatch(setclientsSelected({ data: clientSelected }))
  //   store.dispatch(setprofilesSelected({ data: profileSelected }))

  //   store.dispatch(setrecruitersSelected({ data: recruiterSelected }))
  //   store.dispatch(setstatussSelected({ data: statusSelected }))

  // }, [ dateSelected, jobIdSelected,  clientSelected, profileSelected,  recruiterSelected, statusSelected])


  // useEffect(() => {

  //   }, [
  //     uniqueDataDate,

  //     uniqueDatajobId,

  //     uniqueDataClient,
  //     uniqueDataProfile,

  //     uniqueDataRecruiter,
  //     uniqueDataStatus,
  //   ]);

  const [showSearchjoblisting, setshowSearchjoblisting] = useState({
    showSearchdate: false,
    showSearchuserId: false,
    showSearchStatus: false,
    showSearchClient: false,
    showSearchRecruiter: false,
    showSearchProfile: false,
  });

  const [isDateFiltered, setIsDateFiltered] = useState(false);
  const [isJobIdFiltered, setIsJobIdFiltered] = useState(false);
  const [isstatusFiltered, setIsStatusFiltered] = useState(false);
  const [isclientFiltered, setIsClientFiltered] = useState(false);
  const [isprofileFiltered, setIsProfileFiltered] = useState(false);
  const [isrecruiterFiltered, setIsRecruiterFiltered] = useState(false);

  const updateFilteredRows = ({
    dateSelected,
    jobIdSelected,
    statusSelected,
    clientSelected,
    recruiterSelected,
    profileSelected,

    setuniqueDataDate,
    setuniqueDatajobId,
    setuniqueDataStatus,
    setuniqueDataClient,
    setuniqueDataRecruiter,
    setuniqueDataProfile,
  }) => {
    let prevfilteredRows = allJobs;

    if (dateSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        dateSelected.includes(row.date_created.toString()),
      );
    }
    if (jobIdSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        jobIdSelected.includes(row.id.toString()),
      );
    }
    if (statusSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        statusSelected.includes(row.job_status.toLowerCase()),
      );
    }
    if (clientSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        clientSelected.includes(row.client?.toLowerCase()),
      );
    }
    if (recruiterSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        recruiterSelected.includes(row.recruiter.toLowerCase()),
      );
    }
    if (profileSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        profileSelected.includes(row.role.toLowerCase()),
      );
    }
    const arrayNames = [
      "dateSelected",
      "jobIdSelected",
      "statusSelected",
      "clientSelected",
      "recruiterSelected",
      "profileSelected",
    ];

    const arrays = [
      dateSelected,
      jobIdSelected,
      statusSelected,
      clientSelected,
      recruiterSelected,
      profileSelected,
    ];
    let NamesOfNonEmptyArray = [];

    arrays.forEach((arr, index) => {
      if (arr.length > 0) {
        // NameOfNonEmptyArray = arrayNames[index];
        NamesOfNonEmptyArray.push(arrayNames[index]);
        // setNonEmptyArray(prev => ([
        //   ...prev,
        //   arrayNames[index]
        // ]))
      }
    });
    // const emptyArraysCount = arrays.filter((arr) => arr.length !== 0).length;

    // let NameOfNonEmptyArray = nameOfNonEmptyArray;
    if (!NamesOfNonEmptyArray.includes("dateSelected")) {
      setuniqueDataDate(() => {
        return Array.from(
          new Set(
            prevfilteredRows?.map((filteredRow) => {
              return filteredRow.date_created;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("jobIdSelected")) {
      setuniqueDatajobId(() => {
        return Array.from(
          new Set(
            prevfilteredRows?.map((filteredRow) => {
              return filteredRow.id;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("statusSelected")) {
      setuniqueDataStatus(() => {
        return Array.from(
          new Set(
            prevfilteredRows?.map((filteredRow) => {
              return filteredRow.job_status;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("clientSelected")) {
      setuniqueDataClient(() => {
        return Array.from(
          new Set(
            prevfilteredRows?.map((filteredRow) => {
              return filteredRow.client;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("recruiterSelected")) {
      setuniqueDataRecruiter(() => {
        return Array.from(
          new Set(
            prevfilteredRows?.map((filteredRow) => {
              return filteredRow.recruiter;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("profileSelected")) {
      setuniqueDataProfile(() => {
        return Array.from(
          new Set(
            prevfilteredRows?.map((filteredRow) => {
              return filteredRow.role;
            }),
          ),
        );
      });
    }
    if (prevfilteredRows) {
      setFilteredRows(prevfilteredRows);
      setBelowCount(prevfilteredRows?.length);
    }
  };

  const handleOkClick = () => {
    setId(1)
    updateFilteredRows({
      dateSelected,
      jobIdSelected,
      statusSelected,
      clientSelected,
      recruiterSelected,
      profileSelected,

      setdateSelected,
      setjobIdSelected,
      setstatusSelected,
      setclientSelected,
      setrecruiterSelected,
      setprofileSelected,

      setSelectAllDate,
      setselectAllForJobId,
      setSelectAllStatus,
      setSelectAllClient,
      setSelectAllRecruiter,
      setSelectAllProfile,

      setuniqueDataDate,
      setuniqueDatajobId,
      setuniqueDataStatus,
      setuniqueDataClient,
      setuniqueDataRecruiter,
      setuniqueDataProfile,
    });
    setIsDateFiltered(dateSelected.length > 0);
    setIsJobIdFiltered(jobIdSelected.length > 0);
    setIsClientFiltered(clientSelected.length > 0);
    setIsProfileFiltered(profileSelected.length > 0);
    setIsRecruiterFiltered(recruiterSelected.length > 0);
    setIsStatusFiltered(statusSelected.length > 0);
  };

  useEffect(() => {
    handleOkClick();
  }, [
    dateSelected,
    jobIdSelected,
    statusSelected,
    profileSelected,
    clientSelected,
    recruiterSelected,
  ]);


  const handleCheckboxChangeForDate = (date_created) => {
    const isSelected = dateSelected.includes(date_created.toLowerCase());
    if (isSelected) {
      setdateSelected(
        dateSelected.filter((d) => d !== date_created.toLowerCase()),
      );
      setSelectAllDate(false);
    } else {
      setdateSelected([...dateSelected, date_created.toLowerCase()]);
      setSelectAllDate(dateSelected.length === uniqueDataDate.length - 1);
    }
  };

  const handleSelectAllForDate = () => {
    const allChecked = !selectAllDate;
    setSelectAllDate(allChecked);

    if (allChecked) {
      setdateSelected(uniqueDataDate.map((d) => d.toString()));
    } else {
      setdateSelected([]);
    }
  };

  const handleCheckboxChangeUser = (id) => {
    const isSelected = jobIdSelected.includes(id);
    if (isSelected) {
      setjobIdSelected((prevSelected) =>
        prevSelected.filter((item) => item !== id),
      );
      setselectAllForJobId(false);
    } else {
      setjobIdSelected((prevSelected) => [...prevSelected, id]);
      setselectAllForJobId(jobIdSelected.length === uniqueDatajobId.length - 1);
    }
  };

  const handleSelectAllForUserId = () => {
    const allChecked = !selectAllForJobId;
    setselectAllForJobId(allChecked);
    if (allChecked) {
      setjobIdSelected(uniqueDatajobId.map((d) => d.toString()));
    } else {
      setjobIdSelected([]);
    }
  };

  const handleCheckboxChangeStatus = (job_status) => {
    const isSelected = statusSelected.includes(job_status);
    if (isSelected) {
      setstatusSelected((prevSelected) =>
        prevSelected.filter((item) => item !== job_status),
      );
      setSelectAllStatus(false);
    } else {
      setstatusSelected((prevSelected) => [...prevSelected, job_status]);
      setSelectAllStatus(statusSelected.length === uniqueDataStatus.length - 1);
    }
  };
  const handleSelectAllForStatus = () => {
    const allChecked = !selectAllStatus;
    setSelectAllStatus(allChecked);

    if (allChecked) {
      setstatusSelected(uniqueDataStatus.map((d) => d.toLowerCase()));
    } else {
      setstatusSelected([]);
    }
  };

  const handleCheckboxChangeClient = (client) => {
    const isSelected = clientSelected.includes(client);
    if (isSelected) {
      setclientSelected((prevSelected) =>
        prevSelected.filter((item) => item !== client),
      );
      setSelectAllClient(false);
    } else {
      setclientSelected((prevSelected) => [...prevSelected, client]);
      setSelectAllClient(clientSelected.length === uniqueDataClient.length - 1);
    }
  };
  const handleSelectAllForClient = () => {
    const allChecked = !selectAllClient;
    setSelectAllClient(allChecked);

    if (allChecked) {
      setclientSelected(uniqueDataClient.map((d) => d.toLowerCase()));
    } else {
      setclientSelected([]);
    }
  };
  const handleCheckboxChangeRecruiter = (recruiter) => {
    const isSelected = recruiterSelected.includes(recruiter);
    if (isSelected) {
      setrecruiterSelected((prevSelected) =>
        prevSelected.filter((item) => item !== recruiter),
      );
      setSelectAllRecruiter(false);
    } else {
      setrecruiterSelected((prevSelected) => [...prevSelected, recruiter]);
      setSelectAllRecruiter(
        recruiterSelected.length === uniqueDataRecruiter.length - 1,
      );
    }
  };
  const handleSelectAllForRecruiter = () => {
    const allChecked = !selectAllRecruiter;
    setSelectAllRecruiter(allChecked);

    if (allChecked) {
      setrecruiterSelected(uniqueDataRecruiter.map((d) => d.toLowerCase()));
    } else {
      setrecruiterSelected([]);
    }
  };

  const handleCheckboxChangeProfile = (role) => {
    const isSelected = profileSelected.includes(role);
    if (isSelected) {
      setprofileSelected((prevSelected) =>
        prevSelected.filter((item) => item !== role),
      );
      setSelectAllProfile(false);
    } else {
      setprofileSelected((prevSelected) => [...prevSelected, role]);
      setSelectAllProfile(
        profileSelected.length === uniqueDataProfile.length - 1,
      );
    }
  };
  const handleSelectAllForProfile = () => {
    const allChecked = !selectAllProfile;
    setSelectAllProfile(allChecked);

    if (allChecked) {
      setprofileSelected(uniqueDataProfile.map((d) => d.toLowerCase()));
    } else {
      setprofileSelected([]);
    }
  };
  const [filteredRows, setFilteredRows] = useState([]);
  const [nameOfNonEmptyArray, setnameOfNonEmptyArray] = useState(null);

  const notify = () => toast.success("Job edited successfully");

  // useEffect(() => {
  //   updateFilteredRows({
  //     dateSelected,
  //     jobIdSelected,
  //     clientSelected,
  //     profileSelected,
  //     recruiterSelected,
  //     statusSelected,

  //     setuniqueDataDate,
  //     setuniqueDatajobId,
  //     setuniqueDataClient,
  //     setuniqueDataProfile,
  //     setuniqueDataRecruiter,
  //     setuniqueDataStatus,
  //   });
  // }, [ dateSelected, jobIdSelected,  clientSelected, profileSelected,  recruiterSelected, statusSelected, updateFilteredRows]);

  const setDashboardDatas = async () => {
    setJobs(jobs);
    const val = jobs.length;
    if (val % 20 != 0) setCountItems(parseInt(val / 20) + 1);
    else setCountItems(parseInt(val / 20));
    // console.log(countItems);

    setLoading(false);
    const activeJobs = jobs;
    // .filter((job) => job.job_status === "Active");
    setuniqueDataDate([...new Set(activeJobs.map((d) => d.date_created))]);
    setuniqueDatajobId([...new Set(activeJobs.map((d) => d.id))]);
    setuniqueDataStatus([...new Set(activeJobs.map((d) => d.job_status))]);
    setuniqueDataClient([...new Set(activeJobs.map((d) => d.client))]);
    setuniqueDataRecruiter([...new Set(activeJobs.map((d) => d.recruiter))]);
    setuniqueDataProfile([...new Set(activeJobs.map((d) => d.role))]);
    setFilteredRows(jobs);
    // setJobs(jobs);
    // console.log(jobs);
    const initial = new Array(jobs.length).fill().map((_, idx) => ({
      id: jobs[idx].id,
      client: false,
      recruiter: false,
      role: false,
    }));
    // console.log(initial);
    setShowItems(initial);
    // console.log(initial);
    setLoading(false);
    setBelowCount(jobs.length);
  };
  useEffect(() => {
    if (localStorage.getItem("page_no")) {
      setId(parseInt(localStorage.getItem("page_no")));
      localStorage.removeItem("page_no");
    }
  }, [filteredRows]);
  useEffect(() => {
    console.log(id);
  }, [id]);

  useEffect(() => {
    if (jobs.length === 0) {
      setLoading(true);
      // getAllJobs();
    } else {
      setDashboardDatas();
      setLoading(false);
    }
  }, [jobs]);


  // const notifydelete = () => toast.success(" Job Deleted  successfully");
  // const notifyError = () => toast.error("Unable to Delete Job");
  const handleDeleteJob = async (id) => {
    // console.log("handleDeleteJob");
    if (!waitForSubmission) {
      setwaitForSubmission(true);
      if (dashboardData && dashboardData.candidates && dashboardData.candidates.length > 0) {
        const isJobAssignedToCandidate = dashboardData.candidates.some((candidate) => {
          if (candidate.job_id === id) {
            console.log(`Match Found! Candidate Job ID: ${candidate.job_id}, Job ID to Delete: ${id}`);
            return true;
          }
          return false;
        });

        if (isJobAssignedToCandidate) {
          setwaitForSubmission(false);
          toast.error("Cannot Delete the Job Post because it is assigned to a candidate.");
          setShowModal1(false);
          return;
        }
      }
      console.log("delete");
      try {
        const response = await fetch(
          // `api/delete_job_post/${id}`, {
          `https://backend.makonissoft.com/delete_job_post/${id}`, {
          // {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        },
        );
        const data = await response.json();
        if (data.status === "success") {
          // setDashboardDatas();
          // getDashboardData()
          getAllJobs().then(() => {
            setShowModal1(false);
            setwaitForSubmission(false);
            toast.success(data.message);
          });
        } else {
          // console.log("Response not ok:", response.statusText);
          setwaitForSubmission(false);
          toast.error(data.message);
        }
      } catch (err) {
        setwaitForSubmission(false);
        toast.error("Something went wrong please try again");
      }
    }
  };

  useEffect(() => {
    const handleClick = (e) => {
      const target = e.target;
      // console.log(target);
      // console.log("click detected");
      // console.log(allJobs);

      const idx = allJobs.findIndex((item) => {
        return (
          item.id.toString() === target.id.substring(0, target.id.length - 1)
        );
      });
      console.log(idx);

      if (idx !== -1) {
        console.log(allJobs[idx]);

        // Update the state of showItems based on the clicked target
        const update = new Array(filteredRows.length)
          .fill()
          .map((_, index) => ({
            id: filteredRows[index].id.toString(),
            client: false,
            recruiter: false,
            role: false,
          }));

        if (target.id.endsWith("1")) {
          update[idx] = {
            ...showItems[idx],
            client: !showItems[idx]?.client,
            recruiter: false,
            role: false,
          };
        } else if (target.id.endsWith("2")) {
          update[idx] = {
            ...showItems[idx],
            client: false,
            recruiter: !showItems[idx]?.recruiter,
            role: false,
          };
        } else if (target.id.endsWith("3")) {
          update[idx] = {
            ...showItems[idx],
            client: false,
            recruiter: false,
            role: !showItems[idx]?.role,
          };
        }
        console.log(update);
        setShowItems(update);
      } else {
        if (
          target.id === "default1" ||
          target.id === "default2" ||
          target.id === "default3"
        )
          return;

        const initial = new Array(allJobs.length).fill().map((_, index) => ({
          id: allJobs[index].id.toString(),
          client: false,
          recruiter: false,
          role: false,
        }));
        setShowItems(initial);
        console.log("outside");
      }
    };

    window.addEventListener("click", handleClick);
    return () => {
      window.removeEventListener("click", handleClick);
    };
  }, [allJobs, filteredRows, showItems]);


  const filteredData = (data) => {
    if (data?.length > 20) {
      const data1 = data.filter(
        (_, idx) => idx + 1 <= id * 20 && idx + 1 > (id - 1) * 20,
      );
      return data1;
    } else {
      return data;
    }
  };

  // Sorting function
   const [sortConfig, setSortConfig] = useState({ key: null, direction: null });
//  old soting functionality
// const handleSort = (key) => {
//   if (sortConfig.key === key && sortConfig.direction === 'asc') {
//     // If already sorted ascending, remove sorting
//     setSortConfig({ key: null, direction: null });
//   } else {
//     // Sort ascending on first click
//     setSortConfig({ key, direction: 'asc' });
//   }
//   setId(1); // Reset to first page
// };

  const handleSort = (key) => {
  if (sortConfig.key === key) {
    if (sortConfig.direction === 'asc') {
      // Second click → descending
      setSortConfig({ key, direction: 'desc' });
    } else if (sortConfig.direction === 'desc') {
      // Third click → reset
      setSortConfig({ key: null, direction: null });
    } else {
      // Should not occur, but fallback
      setSortConfig({ key, direction: 'asc' });
    }
  } else {
    // First click on a new column → ascending
    setSortConfig({ key, direction: 'asc' });
  }

  setId(1); // Reset pagination
};

  // Get sort icon based on current sort state
  // old sorting functionality
//   const getSortIcon = (columnKey) => {
//   const isActive = sortConfig.key === columnKey;
//   const rotation = isActive && sortConfig.direction === 'asc' ? '0deg' : 'none';

//   return (
//     <BiSort
//       className="sort-icon"
//       style={{
//         fontSize: '16px',
//         marginLeft: '5px',
//         fontWeight: '800',
//         color: isActive ? 'orange' : '#fff',
//         transform: rotation,
//         transition: 'transform 0.2s ease',
//       }}
//     />
//   );
// };

const getSortIcon = (columnKey) => {
  const isActive = sortConfig.key === columnKey;

  if (!isActive) {
    return <BiSort className="sort-icon" style={{ color: '#fff',   fontWeight: '800', fontSize: '16px', marginLeft: '5px' }} />;
  }

  if (sortConfig.direction === 'desc') {
    return <BiSortUp className="sort-icon" style={{ color: 'orange',   fontWeight: '800', fontSize: '19px', marginLeft: '5px' }} />;
  }

  if (sortConfig.direction === 'asc') {
    return <BiSortDown className="sort-icon" style={{ color: 'orange',   fontWeight: '800', fontSize: '19px', marginLeft: '5px' }} />;
  }

  return <BiSort className="sort-icon" style={{ color: '#fff',   fontWeight: '800', fontSize: '16px', marginLeft: '5px' }} />;
};

  const filteredJobs = useCallback(() => {
    // console.log("filteredRows:", filteredRows);
    const data = filteredRows?.filter((item) => {
      if (filteredId.length > 0) {
        for (const it of filteredId) {
          if (it === item.id) {
            return true;
          }
        }
        // Return false only if none of the elements in filteredId match item.id
        return false;
      } else {
        // console.log('zero size');
        if (searchValue === "") return true;
        else return false;
      }
    });
    // .filter((_, idx) => idx + 1 <= id * 60 && idx + 1 > (id - 1) * 60);

    let sortedData = data;

    // Apply sorting if sortConfig is set
    if (sortConfig.key && data) {
      sortedData = [...data].sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];

        // Handle null/undefined values
        if (aValue == null && bValue == null) return 0;
        if (aValue == null) return sortConfig.direction === 'asc' ? 1 : -1;
        if (bValue == null) return sortConfig.direction === 'asc' ? -1 : 1;

        // Handle different data types
        let comparison = 0;

        if (sortConfig.key === 'date_created') {
          // Sort dates
          comparison = new Date(aValue) - new Date(bValue);
        } else if (sortConfig.key === 'id' || sortConfig.key === 'no_of_positions') {
          // Sort numbers
          comparison = Number(aValue) - Number(bValue);
        } else {
          // Sort strings (case insensitive)
          const aStr = String(aValue).toLowerCase();
          const bStr = String(bValue).toLowerCase();
          comparison = aStr.localeCompare(bStr);
        }

        return sortConfig.direction === 'asc' ? comparison : -comparison;
      });
    }

    const data1 = filteredData(sortedData);
    return data1;
  }, [filteredRows, filteredId, id, sortConfig]);
  function fun(jobs) {
    // const val = jobs.length
    // setBelowCount(val)
    // if (val % 60 != 0)
    //   setCountItems(parseInt(val / 60) + 1)
    // else
    //   setCountItems(parseInt(val / 60))
    // console.log(countItems);
    // // setLoading(false)
    // console.log(jobs);
    const list = jobs.filter((it) => {
      return filteredRows.some((item) => item.id === it.id);
    });
    // console.log(list);
    setBelowCount(list?.length);
    if (searchValue === "") {
      if (localStorage.getItem("page_no")) {
        setId(localStorage.getItem("page_no"));
        localStorage.removeItem("page_no");
      }
    } else {
      setId(1);
    }
    // setuniqueDataDate([...new Set(list.map((d) => d.date_created))]);
    // setuniqueDatajobId([...new Set(list.map((d) => d.id))]);
    // setuniqueDataStatus([...new Set(list.map((d) => d.job_status))]);
    // setuniqueDataClient([...new Set(list.map((d) => d.client))]);
    // setuniqueDataRecruiter([...new Set(list.map((d) => d.recruiter))]);
    // setuniqueDataProfile([...new Set(list.map((d) => d.role))]);
    // setFilteredRows(jobs);
    // setAllJobs(jobs);
    // console.log(jobs)
    // console.log(filteredRows);

    const initial = new Array(jobs.length).fill().map((_, idx) => ({
      id: jobs[idx].id,
      client: false,
      recruiter: false,
      role: false,
    }));
    // console.log(initial);
    setShowItems(initial);
  }

  function extractKeyValuePairs(object, keysToExtract) {
    return keysToExtract.reduce((acc, key) => {
      if (key in object) {
        acc[key] = object[key];
      }
      return acc;
    }, {});
  }


  const getPageRange = () => {
    const pageRange = [];
    const maxPagesToShow = 5; // Adjust this value to show more or fewer page numbers

    // Determine the start and end page numbers to display
    let startPage = Math.max(1, id - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(countItems, startPage + maxPagesToShow - 1);

    // Adjust startPage and endPage if near the beginning or end
    if (endPage - startPage < maxPagesToShow - 1) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    // Include ellipsis if necessary
    if (startPage > 1) {
      pageRange.push(1);
      if (startPage > 2) {
        pageRange.push("...");
      }
    }

    // Add page numbers to the range
    for (let i = startPage; i <= endPage; i++) {
      pageRange.push(i);
    }

    // Include ellipsis if necessary
    if (endPage < countItems) {
      if (endPage < countItems - 1) {
        pageRange.push("...");
      }
      pageRange.push(countItems);
    }

    return pageRange;
  };
  const goToPage = (pageNumber) => {
    if (pageNumber >= 1 && pageNumber <= countItems) {
      console.log("checkpoint3");
      setId(pageNumber);
    }
  };
  useEffect(() => {
    // console.log(filteredRows);
    // console.log(searchValue);
    if (allJobs?.length > 0) {
      // console.log("in if block");
      const update = allJobs?.filter((item) => {
        const extractedObj = extractKeyValuePairs(item, [
          "id",
          "date_created",
          "job_status",
          "client",
          "recruiter",
          "role",
        ]);
        // console.log(extractedObj);
        for (const key in extractedObj) {
          // console.log("key", key);
          let val = extractedObj[key];
          // console.log(val);
          if (val !== null && val !== undefined) {
            if (typeof val !== "string") {
              val = val.toString();
            }
            if (val.toLowerCase().includes(searchValue.toLowerCase())) {
              // console.log("yes working good");
              return true;
            }
          } else {
            // console.log('Value is null or undefined for key:', key);
          }
        }
        // console.log('No match found for searchValue:', searchValue);
        return false;
      });
      // console.log(update)
      fun(update);
      let tempList = [];
      for (const item of update) {
        tempList.push(item.id);
      }
      setFilteredId(tempList);
    }
  }, [filteredRows, searchValue]);
  // useEffect(()=>{
  //   if(localStorage.getItem('page_no')){
  //     console.log('checkpoint4')
  //     setId(localStorage.getItem('page_no'))
  //     localStorage.removeItem('page_no')
  //   }

  // },[])
  useEffect(() => {
    console.log(belowCount);
    if (belowCount % 20 != 0) setCountItems(parseInt(belowCount / 20) + 1);
    else setCountItems(parseInt(belowCount / 20));
  }, [belowCount]);

  const jdApiCall = async (item) => {
    console.log("Fetching jd_file...");
    try {
      const response = await fetch(

        `https://backend.makonissoft.com/view_jd/${item.id}`,
        {
          method: "GET",
        },
      );
      if (response.ok) {
        const blob = await response.blob();
        console.log(blob,"downloadresume");
        const url = URL.createObjectURL(blob);
        window.open(url, "_blank");
      } else {
        console.log("Failed to fetch jd_file:", response.statusText);
      }
    } catch (err) {
      console.log("Error fetching jd_file:", err);
    }
  };


  const removeAllFilter = () => {
    setdateSelected([])
    setjobIdSelected([])
    setstatusSelected([])
    setclientSelected([])
    setrecruiterSelected([])
    setprofileSelected([])
  }
  const handleDownload = () => {
    //  console.log(filteredJobs(), "bfjgbjdfbgj");
    const isFilterApplied = searchValue !== "";

    // Use `filteredRows` if a search value exists, otherwise use `items`
    const jobs = isFilterApplied ? filteredJobs() : filteredRows;

    // Check if dashboardData exists and contains the expected user and candidates properties
    if (!jobs || jobs.length === 0) {
      console.error("No candidates data available");
      return;
    }

    // Get the candidates data
    // const jobs = filteredJobs();
    const jobswithserialnumber = jobs.map((jobs, index) => ({
      "S.No": index + 1, // Serial number starts from 1
      ...jobs,       // Spread the existing candidate data
    }));

    console.log(jobswithserialnumber, "bshdbf");
    // Create a workbook and worksheet from the candidates data
    const ws = XLSX.utils.json_to_sheet(jobswithserialnumber);

    // Set the file name for the Excel file
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'jobs Data');

    // Export the Excel file
    XLSX.writeFile(wb, 'jobs_data.xlsx');
  };



  //  select certain client jobs only
  const [jobStatus, setJobStatus] = useState("Active");
  const [statusChanged, setStatusChanged] = useState(false);
  const [selectedFields, setSelectedFields] = useState([]);
  const [selectedCandidate, setSelectedCandidate] = useState([]);
  const [showModals, setShowModals] = useState(false);
  const [currentClient, setCurrentClient] = useState(null);
  const [selectedJobs, setSelectedJobs] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [waitForSubmission2, setwaitForSubmission2] = useState(false);
  const [selectedJobDetails, setSelectedJobDetails] = useState([]);
  const [showDiv, setShowDiv] = useState(false);
  const [jobStatusselect, setJobStatusselect] = useState("");

  //   const [initialJobStatuses, setInitialJobStatuses] = useState({});

  // useEffect(() => {
  //   const initialStatuses = {};
  //   selectedJobDetails.forEach((job) => {
  //     initialStatuses[job.id] = job.job_status;
  //   });
  //   setInitialJobStatuses(initialStatuses);
  // }, [selectedJobDetails]);

  const handleStatusChange = (e, jobId) => {
    const newStatus = e.target.value;
    setStatusChanged(true);
    setSelectedJobDetails((prevDetails) =>
      prevDetails.map((job) =>
        job.id === jobId ? { ...job, job_status: newStatus } : job
      )
    );
  };


  // const handleStatusChange = (e) => {
  //   setJobStatus(e.target.value);
  //   setStatusChanged(true);
  //   setErrorMessage(""); // Clear error message on status change
  // }

  const handleSelectJobs = (event) => {
    const selectedValue = event.target.value;
    setJobStatusselect(selectedValue);
    setShowDiv(selectedValue === "Select All");
  };
  const handleCheckboxChange = (event) => {
    setShowDiv(event.target.checked);
  };
  // console.log(jobStatusselect,"selected jobs for tosffguhbsus")
  const handleGlobalStatusChange = (e) => {
    const newStatus = e.target.value;
    setJobStatus(newStatus);
    setStatusChanged(true);
    setSelectedJobDetails((prevDetails) =>
      prevDetails.map((job) => ({
        ...job,
        job_status: newStatus,
      }))
    );
  };



  const newopenModal = () => {
    // console.log('Selected Jobs:', selectedJobs);

    const selectedDetails = jobs.filter((item) => selectedJobs.includes(item.id));
    // console.log('Selected candidate Details:', selectedDetails);
    setShowDiv(false)
    // Extract name and id for selected candidates
    const selectedCandidates = selectedDetails.map((item) => ({
      id: item.id,
      name: item.role,
    }));

    // console.log('Selected Candidates (Name and ID):', selectedCandidates);

    // Logging selected fields for each job
    const selectedFieldsForJobs = selectedDetails.map((job) => {
      const selectedFieldsForJob = selectedFields[job.id] || [];
      // console.log(`Selected Fields for Job ${job.job_id}:`, selectedFieldsForJob);
      return {
        job_id: job.id,
        selected_fields: selectedFieldsForJob,
      };
    });
    // console.log('Selected Fields for All Jobs:', selectedFieldsForJobs);

    // Set the extracted details into separate state
    setSelectedCandidate(selectedCandidates);

    setSelectedJobDetails(selectedDetails);
    setShowModals(true);
  };

  useEffect(() => {
    if (selectedJobs.length === 0) {
      // console.log("All jobs deselected. Resetting currentClient to null.");
      setCurrentClient(null);
    }
  }, [selectedJobs]);


  // Close modal
  const newcloseModal = () => {
    setShowModals(false);
    setSelectedJobs([]);
    setSelectedFields([]);
    setSelectedJobDetails([])
  };

  const handleCheckboxClick = (item) => {
    toggleRowSelection(item); // This will toggle the selection when checkbox is clicked
  };
  const toggleRowSelection = (item) => {
    // console.log("Before updating state:");
    // console.log("selectedJobs:", selectedJobs);
    // console.log("currentClient:", currentClient);

    // If no client is set, initialize with the current candidate's client
    if (currentClient === null) {
      setCurrentClient(item.client);
      setSelectedJobs((prevSelectedJobs) => [...prevSelectedJobs, item.id]);
    } else if (item.client !== currentClient) {
      toast.error("You can only select candidates with the same client.");
      return;
    } else {
      setSelectedJobs((prevSelectedJobs) => {
        const isSelected = prevSelectedJobs.includes(item.id);
        const newSelectedJobs = isSelected
          ? prevSelectedJobs.filter((itemId) => itemId !== item.id)
          : [...prevSelectedJobs, item.id];

        // If no jobs remain selected, reset currentClient
        if (newSelectedJobs.length === 0) {
          setCurrentClient(null);
        }

        return newSelectedJobs;
      });
    }

    // console.log("After updating state:");
    // console.log("selectedJobs:", selectedJobs);
    // console.log("currentClient:", currentClient);
  };
  const [selectedStoreddata, setselectedStoreddata] = useState()
  const handleSelectField = (jobId, fieldName, value) => {
    setSelectedFields((prevSelectedFields) => {
      const updatedSelectedFields = { ...prevSelectedFields };

      // Check if the field is selected globally (for all jobs)
      if (updatedSelectedFields[fieldName]) {
        // If it is selected, we need to deselect it (remove from all jobs)
        delete updatedSelectedFields[fieldName];  // Deselect globally

        // Deselect for all individual jobs
        selectedJobDetails.forEach((job) => {
          delete updatedSelectedFields[job.id]?.[fieldName];
        });
      } else {
        // Otherwise, globally select this field and update it for all jobs
        updatedSelectedFields[fieldName] = value || "No Value"; // Default to "No Value" for empty values

        // Update all jobs with the same value for this field
        selectedJobDetails.forEach((job) => {
          if (!updatedSelectedFields[job.id]) {
            updatedSelectedFields[job.id] = {};
          }
          updatedSelectedFields[job.id][fieldName] = value || "No Value";  // Handle empty value
        });
      }

      // console.log('Updated Selected Fields:', updatedSelectedFields);
      return updatedSelectedFields;
    });
  };
  const handleUnselectJob = (jobId) => {
    // Update selectedJobDetails by filtering out the unselected job
    const updatedJobDetails = selectedJobDetails.filter(job => job.id !== jobId);
    setSelectedJobDetails(updatedJobDetails);

    // Update selectedJobs to remove the deselected job id
    setSelectedJobs((prevSelectedJobs) => prevSelectedJobs.filter((id) => id !== jobId));
  };

  const handleSubmit = async (e) => {
    if (!waitForSubmission2) {
      setwaitForSubmission2(true);
      e.preventDefault();


      const jobUpdates = selectedJobDetails.map((job) => ({
        job_id: job.id,
        job_status: job.job_status, // Default if status is not changed
      }));

      console.log(statusChanged, ": the validation befort")
      console.log(jobStatus, ": the validation befort")
      if (!statusChanged) {
        toast.error("Please change the job status before submitting.");
        setwaitForSubmission2(false);
        return;
      }

      try {
        const response = await fetch(
          // `api/update_job_status/${item.id}`, {
          "https://backend.makonissoft.com/update_job_status",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              user_id: localStorage.getItem("user_id"),
              job_ids: selectedJobs, // Send selected job IDs
              job_status: jobUpdates,
            }),
          },
        );

        const data = await response.json();
        if (data.status === "success") {
          getAllJobs().then(() => {
            notify(data.message);
            setwaitForSubmission2(false);
            setShowModals(false);
            setSelectedJobs([]);
            setSelectedFields([]);
            setStatusChanged(false)
            setJobStatus()
            setShowDiv(false)
            setSelectedJobDetails([]);
            navigate("/JobListing");
            // getDashboardData();
          });
          // getDashboardData()
        } else {
          toast.error(data.message);
          setwaitForSubmission2(false);
        }
      } catch (error) {
        console.error("Error updating job status:", error);
        setwaitForSubmission2(false);
        // notifyError("Error Occurred please Try Again");
        // Handle network errors or other exceptions here
      }
    }
  };


  const [isAnimating, setIsAnimating] = useState(false);

  // Sorting state
 

  useEffect(() => {
    setIsAnimating(true);
    setTimeout(() => {
      setIsAnimating(false);
    }, 500); // Adjust the timeout to match your animation duration
  }, [location]);
  return (
    <div className="wrapper">
      <LeftNav />
      <div className="section">
        <TitleBar />
        {loading ? (
          <div className="loader-container">
            <Hourglass
              // visible={true}
              height="60"
              width="340"
              ariaLabel="hourglass-loading"
              wrapperStyle={{}}
              wrapperClass=""
              colors={["#306cce", "#72a1ed"]}
            />
          </div>
        ) : (
          <>
            <div
              className="mobiledash"
            // className={`mobiledash ${isAnimating ? 'genie-effect' : ''}`}

            >
              <label
                style={{
                  marginTop: "1vh",
                  fontWeight: "500",
                  paddingRight: "5px",
                }}
              >
                {/* search */}
              </label>
              <div style={{ display: 'flex', alignItems: 'center', zIndex: '2' }}>
                <button onClick={handleDownload} style={{ display: 'flex', marginRight: '10px', padding: '3px', justifyContent: 'center', alignItems: 'center', borderRadius: "5px", marginTop: "5px", border: "none", height: '30px', width: "35px" }}>
                  <PiMicrosoftExcelLogoFill style={{ marginRight: "0px", fontSize: "25px", color: "#32406d" }} data-tooltip-id={"remove_search"} data-tooltip-content="Data Download" /> {/* Add icon and some styling */}
                  <ReactTooltip
                    style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                    place="top-start"
                    id="Resume Upload"
                  />
                </button>
                <button
                  style={{ cursor: selectedJobs.length === 0 ? 'not-allowed' : 'pointer', height: '30px', width: "30px", color: "orange", marginRight: "10px", border: "none", borderRadius: "5px", marginTop: "5px" }}
                  disabled={selectedJobs.length === 0}
                >
                  <TbStatusChange onClick={newopenModal} style={{ display: 'flex', alignItems: 'center', height: "22px", width: "22px", marginTop: "0px", marginLeft: "3px" }} disabled={selectedJobs.length === 0} data-tooltip-id={"remove_search"} data-tooltip-content="Edit Job Status" />
                  <ReactTooltip
                    style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                    place="top-start"
                    id="Resume Upload"
                  />

                </button>
                <div className="remove_filter_icons"
                  onClick={() => setSelectedJobs([])}
                  style={{
                    display: 'flex',
                    marginRight: '10px',
                    padding: '3px',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: "5px",
                    marginTop: "4px",
                    position: 'relative'
                  }}>
                  {/* Notification Badge */}
                  {selectedJobs.length > 0 && (
                    <span style={{
                      position: 'absolute',
                      top: '-5px',
                      right: '-5px',
                      backgroundColor: 'red',
                      color: 'white',
                      fontSize: '10px',
                      padding: '2px 6px',
                      borderRadius: '50%',
                      zIndex: 1,
                      fontWeight: 'bold'
                    }}>
                      {selectedJobs.length}
                    </span>
                  )}

                  {/* Clear All Icon */}
                  <VscClearAll
                    style={{
                      cursor: 'pointer',
                      height: '24px',
                      width: "24px",
                      color: "#32406d"
                    }}
                    data-tooltip-id={"remove_search"}
                    data-tooltip-content="Unselect"
                  />
                  <ReactTooltip
                    style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                    place="top-start"
                    id="remove_search"
                  />
                </div>
                <IoMdSearch style={{ display: 'flex', alignItems: 'center', height: "22px", width: "22px", marginRight: "-25px", marginTop: "5px" }} />
                <input
                  placeholder="Search"
                  style={{
                    marginTop: "4px",
                    paddingLeft: "26px",
                    height: "30px",
                    width: "200px",
                    backgroundColor: "rgba(255, 255, 255, 0.80)",
                    border: "none",
                    borderRadius: "5px",
                    padding: "0 25px"
                  }}
                  className="searching"
                  value={searchValue}
                  onChange={(e) => {
                    // console.log(e.target.value);
                    setSearchValue(e.target.value);
                    // date_created    job_id    name    email   mobile   client    profile    skills    recruiter    status
                  }}
                />
                {/* <button style={{marginLeft:'20px',backgroundColor: "#32406d",color:'white',border:'none',padding:'4px',borderRadius:'5px'}}
                  onClick={removeAllFilter}
>clear all filters</button> */}
                {/* <img style={{marginLeft:'20px',height:'24px'}} src={clear_search} alt="svg_img" /> */}
                <div className="remove_filter_icons" onClick={() => {
                  setSearchValue('');
                }} style={{ display: 'flex', marginLeft: '10px', padding: '3px', justifyContent: 'center', alignItems: 'center', borderRadius: "5px", marginTop: "4px" }}>
                  {/* <img style={{ cursor: 'pointer', height: '24px' }} src={clear_search} alt="svg_img"
                        data-tooltip-id={"remove_search"}
                        data-tooltip-content="Clear search"
                    /> */}
                  <MdOutlineYoutubeSearchedFor style={{ cursor: 'pointer', height: '24px', width: "24px", color: "#32406d" }} data-tooltip-id={"remove_search"}
                    data-tooltip-content="Clear search" />
                  <ReactTooltip
                    style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                    place="top-start"
                    id="remove_search"
                  />
                </div>
                <div className="remove_filter_icons" onClick={removeAllFilter} style={{ display: 'flex', marginLeft: '10px', padding: '3px', justifyContent: 'center', alignItems: 'center', borderRadius: "5px", marginTop: "4px" }}>
                  <img style={{ cursor: 'pointer', height: '24px' }} src={filter_icon} alt="svg_img"
                    data-tooltip-id={"remove_filter"}
                    data-tooltip-content="Clear all filters"
                  />
                  <ReactTooltip
                    style={{ zIndex: 999, padding: "4px", backgroundColor: "#32406d" }}
                    place="top-start"
                    id="remove_filter"
                  />
                </div>
              </div>

            </div>
            <div
              className="dashr"
            // className={`dashr ${isAnimating ? 'genie-effect ' : ''}`}
            >
              <h5
                id="theader"
                className="joblisthead"
                style={{ padding: "0px", fontSize: "18px", fontWeight: "700", margin: "-35px 0 10px" }}
              >
                All Job Posts
              </h5>
            </div>
            {/* <input style={{height:'10px'}} value={searchValue} onChange={(e)=>{setSearchValue(e.target.value)}} /> */}
            <div
              className="container"
            //  class= {`container ${isAnimating ? 'genie-effect ' : ''}`}
            >
              <div
                className="table-container"
                style={{
                  overflowY: "auto",
                  marginTop: "3px",
                  overflowX: "auto",
                }}
              >
                <table
                  className="max-width-fit-content table"
                  style={{
                    tableLayout: "fixed",
                    width: "100%",
                    marginTop: "-5px",
                  }}
                  class="table"
                  id="candidates-tabl"
                >
                  <thead className="managmentjob">
                    <tr>
                      <th style={{ width: "110px", color: showSearchjoblisting.showSearchdate ? "orange" : "white", }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <span
                            style={{ cursor: "pointer" }}
                            id={"job_label_postRef"}
                            onClick={() => {
                              setshowSearchjoblisting((prev) => ({
                                ...Object.fromEntries(

                                  Object.keys(prev).map((key) => [
                                    key,
                                    key === "showSearchdate"
                                      ? !prev.showSearchdate
                                      : false,
                                  ]),
                                ),
                              }));
                            }}
                          >Job Posted</span>
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <span
                              onClick={() => handleSort('date_created')}
                              style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                            >
                              {getSortIcon('date_created')}
                            </span>
                            <MdFilterAlt
                              style={{ color: isDateFiltered ? "orange" : "white"}}
                              id={"job_postRef"}
                              className="arrow"
                          onClick={() => {
                            setshowSearchjoblisting((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchdate"
                                    ? !prev.showSearchdate
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                          </div>
                        </div>
                        {showSearchjoblisting.showSearchdate && (
                          <div ref={uniRef} className="Filter-popup">
                            <form
                              id="filter-form"
                              className="Filter-inputs-container"
                            >
                              <ul>
                                <li>
                                  <input
                                    type="checkbox"
                                    style={{
                                      width: "12px",
                                      marginRight: "5px",
                                    }}
                                    checked={selectAllDate}
                                    onChange={handleSelectAllForDate}
                                  />
                                  <label
                                    style={{
                                      marginBottom: "0px",
                                      fontWeight: "400",
                                      fontSize: '13px',
                                      cursor: 'pointer',
                                    }}
                                    onClick={() => handleSelectAllForDate()}>
                                    Select all
                                  </label>
                                </li>
                                <li>
                                  {uniqueDataDate
                                    // .sort((a, b) => {
                                    //   const inArray2a = dateSelected.includes(a);
                                    //   const inArray2b = dateSelected.includes(b);

                                    //   if (inArray2a && !inArray2b) {
                                    //     return -1;
                                    //   }
                                    //   else if (!inArray2a && inArray2b) {
                                    //     return 1;
                                    //   } else {
                                    //     return new Date(b) - new Date(a);
                                    //   }

                                    // })
                                    .map((date_created, index) => (
                                      <div key={index} className="filter-inputs">
                                        <input
                                          type="checkbox"
                                          style={{
                                            width: "12px",
                                          }}
                                          checked={dateSelected.includes(
                                            date_created,
                                          )}
                                          onChange={() =>
                                            handleCheckboxChangeForDate(
                                              date_created,
                                            )
                                          }
                                        />
                                        <label
                                          style={{ marginBottom: "0px", cursor: 'pointer' }}
                                          onClick={() => handleCheckboxChangeForDate(
                                            date_created,
                                          )}>
                                       {(() => {
    const [year, month, day] = date_created.split("-");
    return `${day}-${month}-${year}`;
  })()}
                                        </label>
                                      </div>
                                    ))}
                                </li>
                              </ul>
                            </form>
                            {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchjoblisting((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                          </div>
                        )}
                        
                      </th>
                      <th
                        style={{
                          width: "100px",
                          fontSize: "13px",
                          display: selectedJobs.length > 0 ? "table-cell" : "none", // Control visibility of the entire column
                        }}
                      >
                        Select
                      </th>
                      <th style={{ width: "80px", color: showSearchjoblisting.showSearchuserId ? "orange" : "white", }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <span
                            id={"job_label_idRef"}
                            style={{ cursor: "pointer" }}
                            onClick={() => {
                              setshowSearchjoblisting((prev) => ({
                                ...Object.fromEntries(
                                  Object.keys(prev).map((key) => [
                                    key,
                                    key === "showSearchuserId"
                                      ? !prev.showSearchuserId
                                      : false,
                                  ]),
                                ),
                              }));
                            }}
                          >Job Id{" "}</span>
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <span
                              onClick={() => handleSort('id')}
                              style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                            >
                              {getSortIcon('id')}
                            </span>
                            <MdFilterAlt
                              style={{
                                color: isJobIdFiltered ? "orange" : "white",
                                fontSize: '16px'
                          
                              }}
                              id={"job_idRef"}
                              className="arrow"
                              onClick={() => {
                                setshowSearchjoblisting((prev) => ({
                                  ...Object.fromEntries(
                                    Object.keys(prev).map((key) => [
                                      key,
                                      key === "showSearchuserId"
                                        ? !prev.showSearchuserId
                                        : false,
                                    ]),
                                  ),
                                }));
                              }}
                            />
                          </div>
                        </div>
                        {showSearchjoblisting.showSearchuserId && (
                          <div ref={uniRef} className="Filter-popup">
                            <form
                              id="filter-form-user"
                              className="Filter-inputs-container"
                            >
                              <ul>
                                <li>
                                  <input
                                    type="checkbox"
                                    style={{
                                      width: "12px",
                                      marginRight: "5px",
                                    }}
                                    checked={selectAllForJobId}
                                    onChange={handleSelectAllForUserId}
                                  />
                                  <label
                                    style={{
                                      marginBottom: "0px",
                                      fontWeight: "400",
                                      fontSize: '13px',
                                      cursor: 'pointer',
                                    }}
                                    onClick={() => handleSelectAllForUserId()}>
                                    Select all
                                  </label>
                                </li>
                                <li>
                                  {uniqueDatajobId
                                    // .sort((a, b) => {
                                    //   const inArray2a = jobIdSelected.includes(a.toString());
                                    //   const inArray2b = jobIdSelected.includes(b.toString());

                                    //   if (inArray2a && !inArray2b) {
                                    //     return -1;
                                    //   }
                                    //   else if (!inArray2a && inArray2b) {
                                    //     return 1;
                                    //   } else {
                                    //     return a - b;
                                    //   }

                                    // })
                                    .map((userId, index) => {
                                      return (
                                        <div
                                          key={index}
                                          className="filter-inputs"
                                        >
                                          <input
                                            type="checkbox"
                                            style={{
                                              width: "12px",
                                            }}
                                            checked={jobIdSelected.includes(
                                              userId.toString(),
                                            )}
                                            onChange={() =>
                                              handleCheckboxChangeUser(
                                                userId.toString(),
                                              )
                                            }
                                          />
                                          <label
                                            style={{ marginBottom: "0px", cursor: 'pointer' }}
                                            onClick={() => handleCheckboxChangeUser(
                                              userId.toString(),
                                            )}>
                                            {userId}
                                          </label>
                                        </div>
                                      );
                                    })}
                                </li>
                              </ul>
                            </form>

                            {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchjoblisting((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(([key]) => [
                                        key,
                                        false,
                                      ]),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                          </div>
                        )}
                      </th>
                      <th style={{ width: "110px", color: showSearchjoblisting.showSearchStatus ? "orange" : "white", }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <span
                            id={"job_label_statusRef"}
                            style={{ cursor: "pointer" }}
                            onClick={() => {
                              setshowSearchjoblisting((prev) => ({
                                ...Object.fromEntries(
                                  Object.keys(prev).map((key) => [
                                    key,
                                    key === "showSearchStatus"
                                      ? !prev.showSearchStatus
                                      : false,
                                  ]),
                                ),
                              }));
                            }}
                          >Job Status{" "}</span>
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <span
                              onClick={() => handleSort('job_status')}
                              style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                            >
                              {getSortIcon('job_status')}
                            </span>
                            <MdFilterAlt
                              style={{
                                color: isstatusFiltered ? "orange" : "white",
                                fontSize: '16px',
                                // marginLeft: '3px'
                              }}
                              id={"job_statusRef"}
                              className="arrow"
                          onClick={() => {
                            setshowSearchjoblisting((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchStatus"
                                    ? !prev.showSearchStatus
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                        </div>
                        </div>
                        {showSearchjoblisting.showSearchStatus && (
                          <div ref={uniRef} className="Filter-popup">
                            <form
                              id="filter-form-client"
                              className="Filter-inputs-container"
                            >
                              <ul>
                                <li>
                                  <input
                                    type="checkbox"
                                    style={{
                                      width: "12px",
                                      marginRight: "5px",
                                    }}
                                    checked={selectAllStatus}
                                    onChange={handleSelectAllForStatus}
                                  />
                                  <label
                                    style={{
                                      marginBottom: "0px",
                                      fontWeight: "400",
                                      fontSize: '13px',
                                      cursor: 'pointer',
                                    }}
                                    onClick={() => handleSelectAllForStatus()}>
                                    Select all
                                  </label>
                                </li>
                                <li>
                                  {uniqueDataStatus
                                  .sort((a, b) =>
  a.replace(/\s+/g, '').toLowerCase().localeCompare(
    b.replace(/\s+/g, '').toLowerCase()
  )
)
                                  .map((job_status, index) => (
                                    <div key={index} className="filter-inputs">
                                      <input
                                        type="checkbox"
                                        style={{
                                          width: "12px",
                                        }}
                                        checked={statusSelected.includes(
                                          job_status.toLowerCase(),
                                        )}
                                        onChange={() =>
                                          handleCheckboxChangeStatus(
                                            job_status.toLowerCase(),
                                          )
                                        }
                                      />
                                      <label style={{ marginBottom: "0px", cursor: 'pointer' }}
                                        onClick={() => handleCheckboxChangeStatus(
                                          job_status.toLowerCase(),
                                        )}>
                                        {job_status}
                                      </label>
                                    </div>
                                  ))}
                                </li>
                              </ul>
                            </form>
                            {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchjoblisting((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                          </div>
                        )}
                          
                      </th>
                      <th style={{ width: "100px", color: showSearchjoblisting.showSearchClient ? "orange" : "white", }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <span
                            style={{ cursor: "pointer" }}
                            id={"client_label_ref"}
                            onClick={() => {
                              setshowSearchjoblisting((prev) => ({
                                ...Object.fromEntries(
                                  Object.keys(prev).map((key) => [
                                    key,
                                    key === "showSearchClient"
                                      ? !prev.showSearchClient
                                      : false,
                                  ]),
                                ),
                              }));
                            }}
                          >Client{" "}</span>
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <span
                              onClick={() => handleSort('client')}
                              style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                            >
                              {getSortIcon('client')}
                            </span>
                            <MdFilterAlt
                              style={{
                                color: isclientFiltered ? "orange" : "white",
                                fontSize: '16px',
                                // marginLeft: '3px'
                              }}
                              id={"client_ref"}
                              className="arrow"
                              onClick={() => {
                                setshowSearchjoblisting((prev) => ({
                                  ...Object.fromEntries(
                                    Object.keys(prev).map((key) => [
                                      key,
                                      key === "showSearchClient"
                                        ? !prev.showSearchClient
                                        : false,
                                    ]),
                                  ),
                                }));
                              }}
                            />
                          </div>
                        </div>
                        {showSearchjoblisting.showSearchClient && (
                          <div ref={uniRef} className="Filter-popup">
                            <form
                              id="filter-form-client"
                              className="Filter-inputs-container"
                            >
                              <ul>
                                <li>
                                  <input
                                    type="checkbox"
                                    style={{
                                      width: "12px",
                                      marginRight: "5px",
                                    }}
                                    checked={selectAllClient}
                                    onChange={handleSelectAllForClient}
                                  />
                                  <label
                                    style={{
                                      marginBottom: "0px",
                                      fontWeight: "400",
                                      cursor: 'pointer',
                                      fontSize: '13px',
                                    }}
                                    onClick={() => handleSelectAllForClient()} >
                                    Select all
                                  </label>
                                </li>
                                <li>
                                  {uniqueDataClient
                                    // .slice()
                                    // .sort((a, b) => {
                                    //   // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                    //   const trimmedA = a?.trim().toLowerCase();
                                    //   const trimmedB = b?.trim().toLowerCase();

                                    //   const inArray2A = clientSelected.includes(trimmedA);
                                    //   const inArray2B = clientSelected.includes(trimmedB);

                                    //   if (inArray2A && !inArray2B) {
                                    //     return -1;
                                    //   } else if (!inArray2A && inArray2B) {
                                    //     return 1;
                                    //   } else {
                                    //     return trimmedA.localeCompare(trimmedB);
                                    //   }
                                    // })   
                                     .sort((a, b) =>
  a.replace(/\s+/g, '').toLowerCase().localeCompare(
    b.replace(/\s+/g, '').toLowerCase()
  )
)
                                    .map((client, index) => (
                                      <div
                                        key={index}
                                        className="filter-inputs"
                                      >
                                        <input
                                          type="checkbox"
                                          style={{
                                            width: "12px",
                                          }}
                                          checked={clientSelected.includes(
                                            client.toLowerCase(),
                                          )}
                                          onChange={() =>
                                            handleCheckboxChangeClient(
                                              client.toLowerCase(),
                                            )
                                          }
                                        />
                                        <label
                                          style={{ marginBottom: "0px", cursor: 'pointer' }}
                                          onClick={() => handleCheckboxChangeClient(
                                            client.toLowerCase(),
                                          )}>
                                          {client}
                                        </label>
                                      </div>
                                    ))}
                                </li>
                              </ul>
                            </form>
                            {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchjoblisting((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                          </div>
                        )}
                      </th>
                      <th style={{ width: "100px" }}>Posted By</th>
                      <th style={{ width: "100px", color: showSearchjoblisting.showSearchRecruiter ? "orange" : "white", }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <span
                            style={{ cursor: "pointer" }}
                            id={"recruiter_label_ref"}
                            onClick={() => {
                              setshowSearchjoblisting((prev) => ({
                                ...Object.fromEntries(
                                  Object.keys(prev).map((key) => [
                                    key,
                                    key === "showSearchRecruiter"
                                      ? !prev.showSearchRecruiter
                                      : false,
                                  ]),
                                ),
                              }));
                            }}
                          >Recruiter{" "}</span>
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <span
                              onClick={() => handleSort('recruiter')}
                              style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                            >
                              {getSortIcon('recruiter')}
                            </span>
                            <MdFilterAlt
                              style={{
                                color: isrecruiterFiltered ? "orange" : "white",
                                fontSize: '16px',
                                // marginLeft: '3px'
                              }}
                              id={"recruiter_ref"}
                              className="arrow"
                              onClick={() => {
                                setshowSearchjoblisting((prev) => ({
                                  ...Object.fromEntries(
                                    Object.keys(prev).map((key) => [
                                      key,
                                      key === "showSearchRecruiter"
                                        ? !prev.showSearchRecruiter
                                        : false,
                                    ]),
                                  ),
                                }));
                              }}
                            />
                          </div>
                        </div>
                        {showSearchjoblisting.showSearchRecruiter && (
                          <div ref={uniRef} className="Filter-popup">
                            <form
                              id="filter-form"
                              className="Filter-inputs-container"
                            >
                              <ul>
                                <li>
                                  <input
                                    type="checkbox"
                                    style={{
                                      width: "12px",
                                      marginRight: "5px",
                                    }}
                                    checked={selectAllRecruiter}
                                    onChange={handleSelectAllForRecruiter}
                                  />
                                  <label
                                    style={{
                                      marginBottom: "0px",
                                      fontWeight: "400",
                                      cursor: 'pointer',
                                      fontSize: '13px',
                                    }}
                                    onClick={() => handleSelectAllForRecruiter()} >
                                    Select all
                                  </label>
                                </li>
                                <li>
                                  {uniqueDataRecruiter
                                    // .filter(
                                    //   (recruiter) =>
                                    //     recruiter != null && recruiter !== "" && recruiter !== undefined
                                    // )
                                    // .slice()
                                    // .sort((a, b) => {
                                    //   // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                    //   const trimmedA = a?.trim().toLowerCase();
                                    //   const trimmedB = b?.trim().toLowerCase();

                                    //   const inArray2A = recruiterSelected.includes(trimmedA);
                                    //   const inArray2B = recruiterSelected.includes(trimmedB);

                                    //   if (inArray2A && !inArray2B) {
                                    //     return -1;
                                    //   } else if (!inArray2A && inArray2B) {
                                    //     return 1;
                                    //   } else {
                                    //     return trimmedA.localeCompare(trimmedB);
                                    //   }
                                    // })
                                    .sort((a, b) =>
  a.replace(/\s+/g, '').toLowerCase().localeCompare(
    b.replace(/\s+/g, '').toLowerCase()
  )
)

                                    .map((recruiter, index) => (
                                      <div
                                        key={index}
                                        className="filter-inputs"
                                      >
                                        <input
                                          type="checkbox"
                                          style={{
                                            width: "12px",
                                          }}
                                          checked={recruiterSelected.includes(
                                            recruiter.toLowerCase(),
                                          )}
                                          onChange={() =>
                                            handleCheckboxChangeRecruiter(
                                              recruiter.toLowerCase(),
                                            )
                                          }
                                        />
                                        <label
                                          style={{ marginBottom: "0px", cursor: 'pointer' }}
                                          onClick={() => handleCheckboxChangeRecruiter(
                                            recruiter?.toLowerCase(),
                                          )}
                                        >
                                          {recruiter}
                                        </label>
                                      </div>
                                    ))}
                                </li>
                              </ul>
                            </form>
                            {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchjoblisting((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                          </div>
                        )}
                      </th>
                      <th style={{ width: "132px", color: showSearchjoblisting.showSearchProfile ? "orange" : "white", }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <span
                            style={{ cursor: "pointer" }}
                            id={"role_label_ref"}
                            onClick={() => {
                              setshowSearchjoblisting((prev) => ({
                                ...Object.fromEntries(
                                  Object.keys(prev).map((key) => [
                                    key,
                                    key === "showSearchProfile"
                                      ? !prev.showSearchProfile
                                      : false,
                                  ]),
                                ),
                              }));
                            }}
                          >
                            Role{" "}
                          </span>
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <span
                              onClick={() => handleSort('role')}
                              style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                            >
                              {getSortIcon('role')}
                            </span>
                            <MdFilterAlt
                              style={{
                                color: isprofileFiltered ? "orange" : "white",
                                fontSize: '16px',
                                // marginLeft: '3px'
                              }}
                              id={"role_ref"}
                          className="arrow"
                          onClick={() => {
                            setshowSearchjoblisting((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchProfile"
                                    ? !prev.showSearchProfile
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                         </div>
                        </div>
                        {showSearchjoblisting.showSearchProfile && (
                          <div ref={uniRef} className="Filter-popup"  style={{
                            overflowY:"auto",
                            overflowX:"auto"
                          }}>
                            <form
                              id="filter-form-role"
                              className="Filter-inputs-container"
                            >
                              <ul>
                                <li>
                                  <input
                                    type="checkbox"
                                    style={{
                                      width: "12px",
                                      marginRight: "5px",
                                    }}
                                    checked={selectAllProfile}
                                    onChange={handleSelectAllForProfile}
                                  />
                                  <label
                                    style={{
                                      marginBottom: "0px",
                                      fontWeight: "400",
                                      cursor: 'pointer',
                                      fontSize: '13px',
                                    }}
                                    onClick={() => handleSelectAllForProfile()} >
                                    Select all
                                  </label>
                                </li>
                                <li>
                                  {uniqueDataProfile
                                    // .slice()
                                    // .sort((a, b) => {
                                    //   // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                    //   const trimmedA = a?.trim().toLowerCase();
                                    //   const trimmedB = b?.trim().toLowerCase();

                                    //   const inArray2A = profileSelected.includes(trimmedA);
                                    //   const inArray2B = profileSelected.includes(trimmedB);

                                    //   if (inArray2A && !inArray2B) {
                                    //     return -1;
                                    //   } else if (!inArray2A && inArray2B) {
                                    //     return 1;
                                    //   } else {
                                    //     return 0;
                                    //   }
                                    // })
                                    .sort((a, b) =>
  a.replace(/\s+/g, '').toLowerCase().localeCompare(
    b.replace(/\s+/g, '').toLowerCase()
  )
)
                                    .map((role, index) => (
                                      <div
                                        key={index}
                                        className="filter-inputs"
                                      >
                                        <input
                                          type="checkbox"
                                          style={{
                                            width: "12px",
                                          }}
                                          checked={profileSelected.includes(
                                            role.toLowerCase(),
                                          )}
                                          onChange={() =>
                                            handleCheckboxChangeProfile(
                                              role.toLowerCase(),
                                            )
                                          }
                                        />
                                        <label
                                          style={{ marginBottom: "0px", cursor: 'pointer' }}
                                          onClick={() => handleCheckboxChangeProfile(
                                            role.toLowerCase(),
                                          )}>
                                          {role}
                                        </label>
                                      </div>
                                    ))}
                                </li>
                              </ul>
                            </form>
                            {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchjoblisting((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                          </div>
                        )}
                         
                      </th>
                      <th style={{ width: "120px", color: "white" }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <span>No of Positions</span>
                          <span
                            onClick={() => handleSort('no_of_positions')}
                            style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                          >
                            {getSortIcon('no_of_positions')}
                          </span>
                        </div>
                      </th>
                      <th style={{ width: "108px" }}>Add Candidate </th>
                      <th style={{ width: "60px" }}>View JD </th>
                      {/* <th style={{ width: "110px" }}>Re-Assign-Job </th> */}
                      {/* <th style={{ width: "100px" }}>Edit Job Status </th> */}
                      <th style={{ width: "110px" }}>Edit Job Posted </th>
                      <th style={{ width: "60px" }}>Delete </th>
                    </tr>
                  </thead>
                  <tbody style={{ overflowY: "auto" }}>
                    {filteredJobs()?.map((item, idx) => {
                      const isDisabled =
                        selectedJobs.length > 0 && currentClient && currentClient !== item.client;
                      const showCheckbox = selectedJobs.length > 0;
                      return (
                        <tr key={item.id} style={{ overflowy: "auto" }}>
                          <td style={{ textAlign: "center" }} onClick={() => toggleRowSelection(item)}>
                          {(() => {
    const [year, month, day] = item.date_created.split("-");
    return `${day}-${month}-${year}`;
  })()}
                          </td>
                          {showCheckbox && (
                            <td>
                              <input
                                type="checkbox"
                                style={{ width: "12px" }}
                                checked={selectedJobs.includes(item.id)}
                                onChange={() => handleCheckboxClick(item)}
                                disabled={isDisabled}
                              />
                            </td>
                          )}
                          <td style={{ textAlign: "left", paddingLeft: "10px" }} onClick={() => toggleRowSelection(item)}>
                            {item.id}
                          </td>
                          <td onClick={() => toggleRowSelection(item)} >
                            <div
                              className="job_status"
                              style={{
                                backgroundColor:
                                  item.job_status === "Active"
                                    ? "green"
                                    : item.job_status === "Hold"
                                      ? "orange"
                                      : "red",
                                borderRadius: "5px",
                                color: "white",
                                width: "70px",
                                marginLeft: "17px",
                              }}
                            >
                              {item.job_status}
                            </div>
                          </td>

                          <td
                            id={showItems[idx]?.id + "1"}
                            style={{ textAlign: "left", paddingLeft: "5px" }}
                            onClick={() => toggleRowSelection(item)}
                          >
                            {item.client}
                            {showItems[idx].client && (
                              <div
                                id={"default1"}
                                style={{
                                  position: "absolute",
                                  Width: "auto",
                                  maxWidth: "350px",
                                  height: "auto",
                                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                                  border: "1px solid #666",
                                  borderRadius: "10px",
                                  padding: "10px",
                                  boxShadow: "0px 0px 10px rgba(0,0,0,0.2)",
                                  zIndex: "9999", // Ensure the div appears above other content
                                  wordWrap: "break-word",
                                  whiteSpace: "normal",
                                }}
                              >
                                {item.client}
                              </div>
                            )}
                          </td>
                          <td  style={{textAlign: "left", paddingLeft: "5px" }} onClick={() => toggleRowSelection(item)}>
                            {item.management}
                          </td>
                  
                          <td
                            onClick={() => toggleRowSelection(item)}
                            id={showItems[idx]?.id + "2"}
                            style={{ textAlign: "left", paddingLeft: "5px" }}
                          >
                            {item.recruiter}
                            {showItems[idx].recruiter && (
                              <div
                                id={"default2"}
                                style={{
                                  position: "absolute",
                                  Width: "auto",
                                  maxWidth: "350px",
                                  height: "auto",
                                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                                  border: "1px solid #666",
                                  borderRadius: "10px",
                                  padding: "10px",
                                  boxShadow: "0px 0px 10px rgba(0,0,0,0.2)",
                                  zIndex: "9999", // Ensure the div appears above other content
                                  wordWrap: "break-word",
                                  whiteSpace: "normal",
                                }}
                              >
                                {item.recruiter}
                              </div>
                            )}
                          </td>
                          <td
                            onClick={() => toggleRowSelection(item)}
                            id={showItems[idx]?.id + "3"}
                            style={{ textAlign: "left", paddingLeft: "5px" }}
                          >
                            {item.role}
                            {showItems[idx].role && (
                              <div
                                id={"default3"}
                                style={{
                                  position: "absolute",
                                  Width: "auto",
                                  maxWidth: "350px",
                                  height: "auto",
                                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                                  border: "1px solid #666",
                                  borderRadius: "10px",
                                  padding: "10px",
                                  boxShadow: "0px 0px 10px rgba(0,0,0,0.2)",
                                  zIndex: "9999", // Ensure the div appears above other content
                                  wordWrap: "break-word",
                                  whiteSpace: "normal",
                                }}
                              >
                                {item.role}
                              </div>
                            )}
                          </td>

                          <td style={{ color: item.no_of_positions == 0 ? 'red' : 'inherit' }} onClick={() => toggleRowSelection(item)}>
                            {item.no_of_positions == 0 ? 'Closed' : item.no_of_positions}
                          </td>
                          <td

                            onClick={() => {

                              if (item["job_status"].toLowerCase() === "active") {

                                let l = [];

                                l.push(item.id);

                                localStorage.setItem("page_no", id);

                                navigate("/JobListing/AddCandidate", {

                                  state: {

                                    id: l,

                                    profile: item.role,

                                    client: item.client,

                                    path: location.pathname,

                                  },

                                });

                                console.log(location.pathname, "pathlocation");

                              }

                            }}
                          >
                            <FaUserPlus
                              data-tooltip-id={
                                item["job_status"].toLowerCase() === "active"
                                  ? "random-tooltip"
                                  : "addcandidate-tooltip"
                              }
                              data-tooltip-content={
                                item["job_status"].toLowerCase() === "hold"
                                  ? "Requirement is on Hold"
                                  : item["job_status"].toLowerCase() === "close"
                                    ? "Requirement is Closed"
                                    : "Status Unknown"
                              }
                              style={{
                                marginRight: "5px",
                                color: "#336699",
                                fontSize: "18px",
                                cursor:
                                  item["job_status"].toLowerCase() === "active" ? "pointer" : "not-allowed",
                              }}
                            /> {/* Icon */}
                            <ReactTooltip
                              style={{ zIndex: 999, padding: "4px" }}
                              place="bottom"
                              variant="error"
                              id="addcandidate-tooltip"
                            />


                          </td>


                          <td >
                            <FontAwesomeIcon
                              className={
                                item["jd_pdf_present"]
                                  ? "view_jd_option"
                                  : "avoid_view_jd_option"
                              }
                              data-tooltip-id={item["jd_pdf_present"] ? "view-jd-tooltip" : "no-jd-tooltip"}
                              data-tooltip-content={item["jd_pdf_present"] ? "View JD" : "JD not available"}
                              icon={faBook}
                              onClick={() => {
                                if (item["jd_pdf_present"]) jdApiCall(item);
                              }}
                              style={{
                                color: "#795548",

                                cursor: item["jd_pdf_present"] ? "pointer" : "not-allowed",
                              }}
                              aria-disabled={!item["jd_pdf_present"]}
                            />
                            <ReactTooltip
                              id="view-jd-tooltip"
                              className={item["jd_pdf_present"] ? "tooltip-green" : "tooltip-red"}
                              style={{ zIndex: 999, padding: "4px", cursor: item["jd_pdf_present"] ? "pointer" : "not-allowed", backgroundColor: item["jd_pdf_present"] ? "green" : "red" }}
                              place="bottom"
                              variant="error"
                            />
                            <ReactTooltip
                              id="no-jd-tooltip"
                              className="tooltip-red"
                              style={{ zIndex: 999, padding: "4px" }}
                              place="bottom"
                              variant="error"
                            />
                          </td>

                          {/* <td
                          onClick={() => {
                            localStorage.setItem("page_no", id);
                            navigate("/ReassignJob", {
                              state: {
                                recruiter: item.recruiter,
                                id: item.id,
                                path: location.pathname,
                              },
                            });
                          }}
                        >
                          <FontAwesomeIcon
                            icon={faRedo}
                            style={{
                              color: "#4caf50",
                              fontSize: "18px",
                              cursor: "pointer",
                            }}
                          />
                        </td> */}
                          {/* <td
                          onClick={() => {
                            localStorage.setItem("page_no", id);
                            navigate("/EditJobStatus", {
                              state: { item, path: location.pathname },
                            });
                            { console.log(location.pathname, "pathlocation") }
                          }}

                        >
                          <FontAwesomeIcon
                            icon={faClock}
                            style={{
                              color: "#ffc107",
                              fontSize: "18px",
                              cursor: "pointer",
                            }}
                          />
                        </td> */}
                          <td
                            onClick={() => {
                              localStorage.setItem("page_no", id);
                              navigate("/EditJobPosting", {
                                state: {
                                  item, path: location.pathname
                                },

                              });
                              { console.log(location.pathname, "pathlocation") }
                            }}
                          >
                            {" "}
                            <FontAwesomeIcon
                              icon={faEdit}
                              style={{
                                color: "#2196f3",
                                fontSize: "18px",
                                cursor: "pointer",
                              }}
                            />
                          </td>
                          <td
                            onClick={() => handleShowModal1(item.id)}
                            style={{
                              cursor: "pointer",
                              textDecoration: "underline",
                              color: "#E15554",
                              display: "flex", // Align icon and text horizontally
                              alignItems: "center", // Center items vertically
                            }}
                            className="trash-icon"
                          >
                            {" "}
                            <FaTrashAlt
                              style={{
                                marginLeft: "15px",
                                fontSize: "18px",
                                cursor: "pointer",
                              }}
                            />
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
                {filteredJobs()?.length === 0 && (
                  <div
                    style={{
                      textAlign: "center",
                      padding: "10px 0px 10px 0px",
                    }}
                  >
                    No data availible in table
                  </div>
                )}
              </div>
            </div>
            <div
              className="dashbottom"
            // className= {`dashbottom ${isAnimating ? 'genie-effect ' : ''}`}
            >
              <div>
                Showing {belowCount === 0 ? 0 : (id - 1) * 20 + 1} to{" "}
                {id * 20 <= belowCount ? id * 20 : belowCount} of {belowCount}{" "}
                entries
              </div>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  marginTop: "10px",


                }}
                className="pagination"
              >
                <ul className="page" style={{
                  display: "flex",
                  alignItems: "center",
                  overflowX: "none", // Enable horizontal scrolling
                  whiteSpace: "nowrap", // Prevent wrapping of child elements
                  padding: "10px",
                }}>
                  <li
                    className="page__btn newpage_btn"
                    style={{
                      padding: "1px 5px",
                      marginRight: "5px",
                      cursor: "pointer",
                      alignItems: "center",
                      color: "#32406d",
                    }}
                    onClick={() => {
                      if (id !== 1) {
                        setId(id - 1);  // Go to previous page if not the first page
                      } else {
                        toast.warn("You have reached the starting page already.", {
                          position: "top-right",
                          autoClose: 3000,
                          hideProgressBar: false,
                          closeOnClick: true,
                          pauseOnHover: true,
                          draggable: true,
                          progress: undefined,
                          theme: "dark",
                          transition: Bounce,
                        });  // Show warning toast if already on first page
                      }
                    }}
                  >
                    <FaAngleLeft style={{ marginTop: "3px" }} />
                  </li>
                  <div className="gap"
                    style={{
                      display: "flex",
                      columnGap: "10px",
                      overflowX: "auto", // Enable horizontal scrolling for the page numbers
                      whiteSpace: "nowrap", // Prevent wrapping of child elements
                      padding: "10px 0",
                    }}>
                    {getPageRange().map((pageNumber, index) => (
                      <button
                        className={
                          pageNumber === id ? "pag_buttons" : "unsel_button"
                        }
                        key={index}
                        onClick={() => goToPage(pageNumber)}
                        style={{
                          fontWeight: pageNumber === id ? "bold" : "normal",
                          marginRight: "10px",
                          color: pageNumber === id ? "white" : "#000000", // Changed text color
                          backgroundColor:
                            pageNumber === id ? "#32406d" : "#ffff", // Changed background color
                          borderRadius: pageNumber === id ? "0.2rem" : "",
                          fontSize: "15px",
                          border: "none",
                          padding: "1px 10px", // Adjusted padding
                          cursor: "pointer", // Added cursor pointer
                        }}
                      >
                        {pageNumber}
                      </button>
                    ))}
                  </div>
                  <li
                    className="page__btn newpage_btn"
                    style={{
                      padding: "1px 5px",
                      cursor: "pointer",
                      color: "#32406d",
                      marginLeft: "3px"
                    }}
                    onClick={() => {
                      if (belowCount > id * 20) setId(id + 1);
                      else {
                        toast.warn("Reached the end of the list", {
                          position: "top-right",
                          autoClose: 3000,
                          hideProgressBar: false,
                          closeOnClick: true,
                          pauseOnHover: true,
                          draggable: true,
                          progress: undefined,
                          theme: "dark",
                          transition: Bounce,
                        });
                        setId(id);
                      }
                    }}
                  >
                    <FaAngleRight style={{ marginTop: "3px" }} />
                  </li>
                </ul>
              </div>
            </div>
          </>
        )}
      </div>
      <Modal
        isOpen={showModal1}
        onRequestClose={handleCloseModal1}
        contentLabel="Delete Confirmation"
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.5)", // Transparent background to show blurred content
            backdropFilter: "blur(0.5px)", // Blur effect for the entire screen
            zIndex: 9999,
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
          content: {
            width: "275px",
            height: "110px",
            margin: "auto",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            background: "white",
            borderRadius: "10px",
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
            padding: "20px 20px 10px",
          },
        }}
      >
        <div className="modal-actions" style={{ marginBottom: "40px",overflow:"hidden" }}>
          <p
            style={{
              fontSize: "17px",
              fontFamily: "roboto",
              fontWeight: "400",
              color: "black",
            }}
          >
            Are you sure you want to Delete?
          </p>
        </div>
        <div style={{ marginTop: "-20px", marginLeft: "40px" }}>
          {!waitForSubmission && (
            <button
              onClick={handleCloseModal1}
              style={{
                backgroundColor: "Red",
                marginRight: "30px",
                color: "white",
                height: "28px",
                borderRadius: "5px",
                border: "none",
                padding: "5px",
                cursor: "pointer",
                width: "50px",
              }}
            >
              No
            </button>
          )}
          <button
            onClick={handleConfirmDelete}
            style={{
              marginRight: "30px",
              backgroundColor: "green",
              color: "white",
              height: "28px",
              borderRadius: "5px",
              border: "none",
              padding: "5px",
              cursor: "pointer",
              width: waitForSubmission ? "100px" : "50px",
            }}
          >
            {!waitForSubmission ? (
              "Yes"
            ) : (
              <ThreeDots
                wrapperClass="ovalSpinner"
                wrapperStyle={{ marginTop: "-10px", marginLeft: "23px" }}
                visible={waitForSubmission}
                height="40"
                width="40"
                color="white"
                ariaLabel="oval-loading"
              />
            )}
          </button>
        </div>
      </Modal>
      <Modal
        isOpen={showModals}
        onRequestClose={newcloseModal}
        contentLabel="Selected Job Details"
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0)", // Transparent background to show blurred content
            backdropFilter: "blur(0.5px)", // Blur effect for the entire screen
            zIndex: 9999,
            position: "fixed",
            top: 30,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            marginLeft: "60px",
          },
          content: {
            backgroundColor: 'white',
            top: '50%',
            left: window.innerWidth <= 542 ? '40%' : '50%',
            right: 'auto',
            bottom: 'auto',
            marginRight: '-50%',
            transform: 'translate(-50%, -50%)',
            maxWidth: window.innerWidth <= 542 ? '100%' : '40vw', // Allow responsive sizing
            width: '100%',
            minHeight: '90vh',
          },
        }}
      >
        <h3 style={{ textAlign: "center" }}>Job Details</h3>

        {selectedJobDetails.length > 0 ? (
          <>
            {/* Display the client heading once */}
            <h3>{selectedJobDetails[0].client}</h3>
            <div
              style={{
                maxHeight: '70vh',
                overflowY: 'auto',
              }}
            >
              {selectedJobDetails.map((job) => (
                <div key={job.id} style={{ marginBottom: '0px' }}>
                  <table style={{ width: '100%' }}>
                    <thead>
                      <tr>
                        <th colSpan="2" style={{ textAlign: 'left', padding: '5px' }}>
                          {job.role} <span style={{ color: '#888' }}>({job.id})</span>
                          <span
                            onClick={() => handleUnselectJob(job.id)}
                            style={{
                              color: 'red',
                              cursor: 'pointer',
                              fontSize: '20px',
                              marginLeft: '10px',
                            }}
                          >
                            &#10005;
                          </span>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {["id", "role", "job_status"].map((key) => {
                        const value = job[key];
                        // const displayValue = value ? value : " - ";
                        const isFieldSelected = selectedFields[key] !== undefined;
                        const displayValue = value ? value : " - ";

                        return (
                          <tr key={key}>
                            <th
                              onClick={() => handleSelectField(job.id, key, value)} // Toggle field selection on click
                              style={{
                                backgroundColor: '#cdcdcd',
                                color: 'rgb(0,0,0)',
                                cursor: 'pointer',
                                padding: "2px 15px",
                                fontSize: "16px",
                                fontWeight: "500",
                                textAlign: "left",
                              }}
                            >
                              {key}:
                            </th>
                            <td
                              onClick={() => handleSelectField(job.id, key, value)} // Toggle field selection on click
                              className="role-cell"
                              style={{
                                backgroundColor: '#cdcdcd',
                                color: 'rgb(0,0,0)',
                                cursor: 'pointer',
                                padding: "2px 15px",
                              }}

                            >

                              {key === "job_status" ? (
                                <select
                                  defaultValue={job.job_status || ""}
                                  onChange={(e) => handleStatusChange(e, job.id)}
                                  disabled={showDiv}
                                  style={{
                                    border: showDiv ? "none" : "visible",
                                    backgroundColor: showDiv ? "#e0e0e0" : "white",
                                    cursor: showDiv ? "not-allowed" : "pointer",
                                  }}
                                >
                                  <option value=""> Select Job Status</option>
                                  <option value="Active">Active</option>
                                  <option value="Hold">Hold</option>
                                  <option value="Close">Close</option>
                                </select>
                              ) : (
                                <span title={displayValue}>{displayValue}</span>
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>

                </div>
              ))}
            </div>
{selectedJobDetails.length > 1 && (
  <div style={{
    position: 'sticky',
    display: 'flex',
    justifyContent: "flex-start",
    zIndex: 1000,
    padding: "1px",
    backgroundColor: "#fff",
    bottom: "20px"
  }}>

    <div style={{
      margin: "5px", display: "flex", justifyContent: "left"
    }}>
      <input
        type="checkbox"
        id="selectAll"
        onChange={handleCheckboxChange}
      />
      <label htmlFor="selectAll" style={{ marginLeft: "5px" }}>
        Change All Job Status
      </label>
    </div>

    {showDiv && (
      <div style={{ margin: "5px" }}>
        <span> : </span>
        <select
          name="job_status"
          id="job_status"
          className="form-select form-control-lg"
          onChange={handleGlobalStatusChange}
          style={{ width: "90%", marginLeft: "5px" }}
        >
          <option value="" disabled selected>
            Select Job Status
          </option>
          <option value="Active">Active</option>
          <option value="Hold">Hold</option>
          <option value="Close">Close</option>
        </select>
      </div>
    )}
  </div>
)}

            {/* <div  style={{display:"flex",justifyContent:"Left",margin:"5px"}}> 
                  <h4> Edit Job Status  <span>:</span></h4> 
                   <select
                          name="job_status"
                          id="job_status"
                          className="form-select form-control-lg"
                          value={jobStatus}
                        
                          onChange={handleGlobalStatusChange}
                          // defaultValue={item.job_status}
                          style={{ width: "100px",marginLeft:"5px" }}
                        >
                          <option value="" selected disabled>
                            Select Job Status
                          </option>
                          <option value="Active">Active</option>
                          <option value="Hold">Hold</option>
                          <option value="Close">Close</option>
                        </select>
                  </div> */}


          </>
        ) : (
          <p>No jobs selected</p>
        )}

        <div
          style={{
            position: 'sticky',
            display: 'flex',
            justifyContent: 'space-between',
            zIndex: 1000,
            padding: "10px",
            backgroundColor: "#fff",
            bottom: "0px"
          }}
        >
          <button
            onClick={newcloseModal}
            style={{
              backgroundColor: "red", // Red for Close
              color: "white",
              borderRadius: "4px",
              cursor: "pointer",
              border: "none",
              padding: "8px 15px"
            }}
          >
            Close
          </button>
          <button
            // onClick={() => setShowEmailModal(true)} // Show the email modal
            onClick={handleSubmit}
            style={{
              backgroundColor: "green", // Green for Share via Email
              color: "white",
              borderRadius: "4px",
              cursor: "pointer",
              border: "none",
          
              padding: "8px 15px"
            }}
          >
            {waitForSubmission2 ? "" : "Update"}
            <ThreeDots
              wrapperClass="ovalSpinner"
              wrapperStyle={{
                position: "relative",
              }}
              visible={waitForSubmission2}
              height="25"
              width="35"
              color="white"
              ariaLabel="oval-loading"
            />
          </button>
        </div>
      </Modal>
    </div>
  );
}
export default JobListing;
